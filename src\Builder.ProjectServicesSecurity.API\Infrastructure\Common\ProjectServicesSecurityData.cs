﻿using Builder.ProjectServicesSecurity.API.Services.Models.Interfaces;

namespace Builder.ProjectServicesSecurity.API.Infrastructure.Common
{
    public class ProjectServicesSecurityData : IProjectServicesSecurityData
    {
        public Guid Id { get; set; }
        public object SyncingEntityId { get; set; }
        public int SourceProjectId { get; set; }
        public int TargetProjectId { get; set; }
        public int TypeId { get; set; }
        public string Username { get; set; }
        public int IndexSourceBP { get; set; }
        public int TotalSourceBP { get; set; }
        public string JiraId { get; set; }
        public int RetailerSeparationRequestId { get; set; }
    }
}
