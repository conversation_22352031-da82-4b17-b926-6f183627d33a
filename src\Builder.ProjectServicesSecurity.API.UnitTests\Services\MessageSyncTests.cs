using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.API.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Castle.Core.Logging;
using AutoFixture;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;
using Xunit;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;

namespace Builder.ProjectServicesSecurity.API.UnitTests.Services
{
    public class MessageSyncTests
    {
        Mock<ILogger<MessageSync>> _mockLogger;
        IMessageSync _msgSyncMock;
        IConfigurationRoot _configuration;
        Mock<IWebAPIService> _webAPIService;

        public MessageSyncTests()
        {
            var configurationSettings = new Dictionary<string, string>
                                        {
                                            { "ExchangeEntitySettings:ProjectServicesSecurityExchangeEnabled", "true" },
                                            { "WebServiceClient:BaseAddress:ProjectServicesAPI", "https://projectservices-api.t1.dwh.in.gfk.com/" },
                                            { "WebServiceClient:BaseAddress:ProjectServicesSecurityAPI", "https://projectservicessecurity-api.t1.dwh.in.gfk.com/" },
                                        };

            var configurationBuilder = new ConfigurationBuilder();
            configurationBuilder.AddInMemoryCollection(configurationSettings);
            _configuration = configurationBuilder.Build();
            _mockLogger = new Mock<ILogger<MessageSync>>();
            _webAPIService = new Mock<IWebAPIService>();

            _msgSyncMock = new MessageSync(_mockLogger.Object, _configuration, _webAPIService.Object);
        }

        [Fact]
        public async Task PerformSync_ProjectSecurityExchange_ShouldExecute_AddSecurityUsers_Returns_Success()
        {
            // Arrange
            var testMessage = @"{
                ""Id"": ""c0a994e3-ca29-4a7a-a214-10ef28879a1e"",
                ""SyncingEntityId"": 10000409,
                ""SourceProjectId"": 10003453,
                ""TargetProjectId"": 10003487,
                ""TypeId"": 2,
                ""Username"": ""ansama"",
                ""IndexSourceBP"": 1,
                ""TotalSourceBP"": 1,
                ""JiraId"": ""STS-123456"",
                ""RetailerSeparationRequestId"": 343
            }";

            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                ProjectSecurityConstants.ProjectSecurityExchange,
                RMQConstants.Update,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(Encoding.UTF8.GetBytes(testMessage)),
                new CancellationToken()
            );

            var userResponse = new UserResponse
            {
                ProjectId = 123456,
                Users = new List<UserHistoryResponse>
                {
                    new UserHistoryResponse
                    {
                        Id = 112233,
                        AssignedBy = "test",
                        AssignedOn = DateTime.UtcNow
                    }
                }
            };

            _webAPIService.Setup(client => client.GetUsersByProjectId(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(userResponse);

            _webAPIService.Setup(client => client.AddSecurityUsers(It.IsAny<ProjectUserRequest>(), "testUser"))
                .ReturnsAsync(It.IsAny<ProjectUserResponse>);

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _mockLogger.Verify(x => x.Log(LogLevel.Information,
                  It.IsAny<EventId>(),
                  It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Request has been processed for Jira Id: STS-123456 and RS Id: 343")),
                  It.IsAny<Exception>(),
                  It.IsAny<Func<It.IsAnyType, Exception, string>>()
              ),
              Times.Once);

        }

        [Fact]
        public async Task PerformSync_ProjectSecurityExchange_ShouldExecute_AddSecurityUsers_Returns_Error()
        {
            // Arrange
            var testMessage = @"{
                ""Id"": ""c0a994e3-ca29-4a7a-a214-10ef28879a1e"",
                ""SyncingEntityId"": ""5"",
                ""SourceProjectId"": 10003453,
                ""TargetProjectId"": 10003487,
                ""TypeId"": 2,
                ""Username"": ""testuser"",
                ""IndexSourceBP"": 1,
                ""TotalSourceBP"": 2,
                ""JiraId"": ""STS-789012"",
                ""RetailerSeparationRequestId"": 456
            }";

            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                ProjectSecurityConstants.ProjectSecurityExchange,
                RMQConstants.Update,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(Encoding.UTF8.GetBytes(testMessage)),
                new CancellationToken()
            );

            _webAPIService
                .Setup(client => client.GetUsersByProjectId(It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(new Exception("Simulated Exception"));

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _mockLogger.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("EXCEPTION - PerformSync")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()
            ), Times.Once);
        }

        [Fact]
        public async Task PerformSync_ShouldSendNotification_WhenRequestIsCompleted()
        {
            // Arrange - QC Security project with IndexSourceBP equals TotalSourceBP
            var testMessage = @"{
                ""Id"": ""c0a994e3-ca29-4a7a-a214-10ef28879a1e"",
                ""SyncingEntityId"": 10000409,
                ""SourceProjectId"": 10003453,
                ""TargetProjectId"": 10003487,
                ""TypeId"": 2,
                ""Username"": ""ansama"",
                ""IndexSourceBP"": 3,
                ""TotalSourceBP"": 3,
                ""JiraId"": ""STS-123456"",
                ""RetailerSeparationRequestId"": 343
            }";

            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                ProjectSecurityConstants.ProjectSecurityExchange,
                RMQConstants.Update,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(Encoding.UTF8.GetBytes(testMessage)),
                new CancellationToken()
            );

            var userResponse = new UserResponse
            {
                ProjectId = 123456,
                Users = new List<UserHistoryResponse>
                {
                    new UserHistoryResponse
                    {
                        Id = 112233,
                        AssignedBy = "test",
                        AssignedOn = DateTime.UtcNow
                    }
                }
            };

            _webAPIService.Setup(client => client.GetUsersByProjectId(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync(userResponse);

            _webAPIService.Setup(client => client.AddSecurityUsers(It.IsAny<ProjectUserRequest>(), It.IsAny<string>()))
                .ReturnsAsync(It.IsAny<ProjectUserResponse>());

            _webAPIService.Setup(client => client.SendNotification(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _webAPIService.Verify(client => client.SendNotification(
                "ansama",
                "Request has been processed for Jira Id: STS-123456 and RS Id: 343",
                343), Times.Once);
        }

        [Fact]
        public async Task PerformSync_ShouldNotSendNotification_WhenRequestIsNotCompleted()
        {
            // Arrange - QC Security project with IndexSourceBP not equal to TotalSourceBP
            var testMessage = @"{
                ""Id"": ""c0a994e3-ca29-4a7a-a214-10ef28879a1e"",
                ""SyncingEntityId"": 10000409,
                ""SourceProjectId"": 10003453,
                ""TargetProjectId"": 10003487,
                ""TypeId"": 2,
                ""Username"": ""ansama"",
                ""IndexSourceBP"": 2,
                ""TotalSourceBP"": 3,
                ""JiraId"": ""STS-123456"",
                ""RetailerSeparationRequestId"": 343
            }";

            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                ProjectSecurityConstants.ProjectSecurityExchange,
                RMQConstants.Update,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(Encoding.UTF8.GetBytes(testMessage)),
                new CancellationToken()
            );

            var userResponse = new UserResponse
            {
                ProjectId = 123456,
                Users = new List<UserHistoryResponse>
                {
                    new UserHistoryResponse
                    {
                        Id = 112233,
                        AssignedBy = "test",
                        AssignedOn = DateTime.UtcNow
                    }
                }
            };

            _webAPIService.Setup(client => client.GetUsersByProjectId(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync(userResponse);

            _webAPIService.Setup(client => client.AddSecurityUsers(It.IsAny<ProjectUserRequest>(), It.IsAny<string>()))
                .ReturnsAsync(It.IsAny<ProjectUserResponse>());

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _webAPIService.Verify(client => client.SendNotification(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task PerformSync_ShouldNotSendNotification_WhenProjectTypeIsNotQCSecurity()
        {
            // Arrange - BP Security project (TypeId = 1) should not trigger notification
            var testMessage = @"{
                ""Id"": ""c0a994e3-ca29-4a7a-a214-10ef28879a1e"",
                ""SyncingEntityId"": 10000409,
                ""SourceProjectId"": 10003453,
                ""TargetProjectId"": 10003487,
                ""TypeId"": 1,
                ""Username"": ""ansama"",
                ""IndexSourceBP"": 3,
                ""TotalSourceBP"": 3,
                ""JiraId"": ""STS-123456"",
                ""RetailerSeparationRequestId"": 343
            }";

            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                ProjectSecurityConstants.ProjectSecurityExchange,
                RMQConstants.Update,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(Encoding.UTF8.GetBytes(testMessage)),
                new CancellationToken()
            );

            var userResponse = new UserResponse
            {
                ProjectId = 123456,
                Users = new List<UserHistoryResponse>
                {
                    new UserHistoryResponse
                    {
                        Id = 112233,
                        AssignedBy = "test",
                        AssignedOn = DateTime.UtcNow
                    }
                }
            };

            _webAPIService.Setup(client => client.GetUsersByProjectId(It.IsAny<int>(), It.IsAny<int>()))
                .ReturnsAsync(userResponse);

            _webAPIService.Setup(client => client.AddSecurityUsers(It.IsAny<ProjectUserRequest>(), It.IsAny<string>()))
                .ReturnsAsync(It.IsAny<ProjectUserResponse>());

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _webAPIService.Verify(client => client.SendNotification(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>()), Times.Never);
        }
    }
}
