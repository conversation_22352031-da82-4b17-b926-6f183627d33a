﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.API.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Castle.Core.Logging;
using AutoFixture;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;
using Xunit;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;

namespace Builder.ProjectServicesSecurity.API.UnitTests.Services
{
    public class MessageSyncTests
    {
        Mock<ILogger<MessageSync>> _mockLogger;
        IMessageSync _msgSyncMock;
        IConfigurationRoot _configuration;
        Mock<IWebAPIService> _webAPIService;

        public MessageSyncTests()
        {
            var configurationSettings = new Dictionary<string, string>
                                        {
                                            { "ExchangeEntitySettings:ProjectServicesSecurityExchangeEnabled", "true" },
                                            { "WebServiceClient:BaseAddress:ProjectServicesAPI", "https://projectservices-api.t1.dwh.in.gfk.com/" },
                                            { "WebServiceClient:BaseAddress:ProjectServicesSecurityAPI", "https://projectservicessecurity-api.t1.dwh.in.gfk.com/" },
                                        };

            var configurationBuilder = new ConfigurationBuilder();
            configurationBuilder.AddInMemoryCollection(configurationSettings);
            _configuration = configurationBuilder.Build();
            _mockLogger = new Mock<ILogger<MessageSync>>();
            _webAPIService = new Mock<IWebAPIService>();

            _msgSyncMock = new MessageSync(_mockLogger.Object, _configuration, _webAPIService.Object);
        }

        [Fact]
        public async Task PerformSync_ProjectSecurityExchange_ShouldExecute_AddSecurityUsers_Returns_Success()
        {
            // Arrange
            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                ProjectSecurityConstants.ProjectSecurityExchange,
                RMQConstants.Update,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(new byte[] { 123, 34, 73, 100, 34, 58, 34, 99, 48, 97, 57, 57, 52, 101, 51, 45, 99, 97, 50, 57, 45, 52, 97, 55, 97, 45, 97, 50, 49, 52, 45, 49, 48, 101, 102, 50, 56, 56, 55, 57, 97, 49, 101, 34, 44, 34, 83, 121, 110, 99, 105, 110, 103, 69, 110, 116, 105, 116, 121, 73, 100, 34, 58, 49, 48, 48, 48, 48, 52, 48, 57, 44, 34, 83, 111, 117, 114, 99, 101, 80, 114, 111, 106, 101, 99, 116, 73, 100, 34, 58, 49, 48, 48, 48, 51, 52, 53, 51, 44, 34, 84, 97, 114, 103, 101, 116, 80, 114, 111, 106, 101, 99, 116, 73, 100, 34, 58, 49, 48, 48, 48, 51, 52, 56, 55, 44, 34, 84, 121, 112, 101, 73, 100, 34, 58, 50, 44, 34, 85, 115, 101, 114, 110, 97, 109, 101, 34, 58, 34, 97, 110, 115, 97, 109, 97, 34, 44, 34, 74, 105, 114, 97, 73, 100, 34, 58, 34, 83, 84, 83, 45, 49, 50, 51, 52, 53, 54, 34, 44, 34, 82, 101, 116, 97, 105, 108, 101, 114, 83, 101, 112, 97, 114, 97, 116, 105, 111, 110, 82, 101, 113, 117, 101, 115, 116, 73, 100, 34, 58, 51, 52, 51, 125 }),
                new CancellationToken()
            );

            var userResponse = new UserResponse
            {
                ProjectId = 123456,
                Users = new List<UserHistoryResponse>
                {
                    new UserHistoryResponse
                    {
                        Id = 112233,
                        AssignedBy = "test",
                        AssignedOn = DateTime.UtcNow
                    }
                }
            };

            _webAPIService.Setup(client => client.GetUsersByProjectId(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(userResponse);

            _webAPIService.Setup(client => client.AddSecurityUsers(It.IsAny<ProjectUserRequest>(), "testUser"))
                .ReturnsAsync(It.IsAny<ProjectUserResponse>);

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _mockLogger.Verify(x => x.Log(LogLevel.Information,
                  It.IsAny<EventId>(),
                  It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Request has been processed for Jira Id: STS-123456 and RS Id: 343")),
                  It.IsAny<Exception>(),
                  It.IsAny<Func<It.IsAnyType, Exception, string>>()
              ),
              Times.Once);

        }

        [Fact]
        public async Task PerformSync_ProjectSecurityExchange_ShouldExecute_AddSecurityUsers_Returns_Error()
        {
            // Arrange
            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                ProjectSecurityConstants.ProjectSecurityExchange,
                RMQConstants.Update,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(Encoding.UTF8.GetBytes("{ \"Id\": \"c0a994e3-ca29-4a7a-a214-10ef28879a1e\", \"SyncingEntityId\": \"5\" }")),
                new CancellationToken()
            );

            _webAPIService
                .Setup(client => client.GetUsersByProjectId(It.IsAny<int>(), It.IsAny<int>()))
                .ThrowsAsync(new Exception("Simulated Exception"));

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _mockLogger.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("EXCEPTION - PerformSync")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()
            ), Times.Once);
        }
    }
}
