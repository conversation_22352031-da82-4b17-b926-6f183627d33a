﻿using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.IntegrationTests.Infrastructure;
using Builder.ProjectServicesSecurity.API.IntegrationTests.Infrastructure.WebAPIClient;
using Builder.ProjectServicesSecurity.API.Persistence;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Configuration;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using RabbitMQ.Client;
using System.Net;
using System.Net.Http.Headers;
using Xunit;
using System.Text;
using System.Text.Json;
using DWH.ProjectServices.API.IntegrationTests.MockService;
using Microsoft.EntityFrameworkCore;
using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using Builder.ProjectServicesSecurity.API.Domain.Enum;
using Builder.ProjectServicesSecurity.API.Domain;
using Xunit.Abstractions;

namespace Builder.ProjectServicesSecurity.API.IntegrationTests.Controllers
{
    [Collection("Non-Parallel Collection")]
    public class ProjectUserControllerTest : IClassFixture<APIFactory>, IAsyncLifetime
    {
        private readonly APIFactory _factory;
        private const string ApiPrefix = "/api/projectuser";
        private const string AppConfig = "appsettings.development.json";
        private string _bearerToken;
        IWebAPIService _webAPIService;
        ITokenService _tokenService;
        PostgreSqlDbContext _postgreSqlDbContext;
        IConnection _connection;
        IChannel _channel;
        private readonly ITestOutputHelper _output;


        public ProjectUserControllerTest(APIFactory apifactory, ITestOutputHelper output)
        {
            _factory = apifactory;
            var serviceProvider = _factory.Services.CreateScope().ServiceProvider;
            _tokenService = serviceProvider.GetRequiredService<ITokenService>();
            _postgreSqlDbContext = serviceProvider.GetRequiredService<PostgreSqlDbContext>();
            _output = output;
            var rabbitMQSettings = Options.Create(new RabbitMQSettings
            {
                HostName = "localhost",
                UserName = "guest",
                Password = "guest",
                VirtualHost = "/"
            });
            _webAPIService = new WebAPIService(_tokenService);
            var connectionFactory = new RabbitMQConnectionFactory(rabbitMQSettings);
            _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
            _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();
        }

        public Task DisposeAsync() => Task.CompletedTask;

        public async Task InitializeAsync()
        {
            _bearerToken = await _webAPIService.GetBearerToken();
        }

        [Fact]
        public async Task AddAsync_ValidRequest_UniqueCase_ReturnsCreated()
        {
            // Arrange
            var _client = _factory.Client;
            RemoveAllRecords();
            SetAuthorizationHeader();

            var request = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 111, 222, 333 },
                UserIds = new List<int> { 123, 456, 789 },
                ProjectTypeId = 1
            };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");
            var sourceCount = request.ProjectIds.Count * request.UserIds.Count;

            // Act
            var response = await _client.PostAsync(ApiPrefix, content);
            var result = await response.Content.ReadFromJsonAsync<ProjectUserResponse>();
            var uniqueMessage = result.ProjectResponses.Any(r => r.StatusMsg == "All users assigned successfully");

            int actualCount;
            actualCount = _postgreSqlDbContext.ProjectUsers.Count();

            // Assert
            response.EnsureSuccessStatusCode();
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            result.Should().NotBeNull();
            sourceCount.Should().Be(actualCount);
            uniqueMessage.Should().BeTrue();
        }



        [Fact]
        public async Task AddAsync_ValidRequest_PartialCase_ReturnsCreated()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();

            var request = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 111, 222, 333 },
                UserIds = new List<int> { 123, 456, 789 },
                ProjectTypeId = 1
            };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            var sourceCount = _postgreSqlDbContext.ProjectUsers.Count();

            var existProjectUser = _postgreSqlDbContext.ProjectUsers
                                .FirstOrDefault(i => i.UserId == 123 && i.ProjectId == 111);

            
            _postgreSqlDbContext.Remove(existProjectUser);
            _postgreSqlDbContext.SaveChanges();

            // Act
            var response = await _client.PostAsync(ApiPrefix, content);
            var result = await response.Content.ReadFromJsonAsync<ProjectUserResponse>();
            var partialMessage = result.ProjectResponses.Any(r => r.StatusMsg == "New users assigned successfully and duplicate users skipped");

            var actualCount = _postgreSqlDbContext.ProjectUsers.Count();

            // Assert
            response.EnsureSuccessStatusCode();
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            result.Should().NotBeNull();
            sourceCount.Should().Be(actualCount);
            partialMessage.Should().BeTrue();
        }


        [Fact]
        public async Task AddAsync_ValidRequest_DuplicateCase_ReturnsCreated()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();
            var request = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 111, 222, 333 },
                UserIds = new List<int> { 123, 456, 789 },
                ProjectTypeId = 1
            };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            var sourceCount = _postgreSqlDbContext.ProjectUsers.Count();

            // Act
            var response = await _client.PostAsync(ApiPrefix, content);
            var result = await response.Content.ReadFromJsonAsync<ProjectUserResponse>();
            var duplicateMessage = result.ProjectResponses.All(r => r.StatusMsg == "User(s) already assigned to this project");

            int actualCount;
            actualCount = _postgreSqlDbContext.ProjectUsers.Count();

            // Assert
            response.EnsureSuccessStatusCode();
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            result.Should().NotBeNull();
            sourceCount.Should().Be(actualCount);
            duplicateMessage.Should().BeTrue();
        }

        [Fact]
        public async Task AddAsync_InValidRequest_ReturnsError()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();
            var request = new ProjectUserRequest
            {
                ProjectIds = null,
                UserIds = null,
                ProjectTypeId = 0
            };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync(ApiPrefix, content);

            // Assert
            Assert.Equal(HttpStatusCode.InternalServerError, response.StatusCode);
            Assert.False(response.IsSuccessStatusCode);
        }

        [Fact]
        public async Task GetUsers_ExistingProject_ReturnsUsers()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();
            
            var qcprojectId = 111;
            var userIds = new List<int>() { 123, 456, 789 };
            var sourceCount = _postgreSqlDbContext.ProjectUsers
                .Count(pu => pu.ProjectId == qcprojectId
                              && userIds.Contains(pu.UserId)
                              && pu.Deleted == false);

            // Act
            var response = await _client.GetAsync($"{ApiPrefix}/{qcprojectId}");

            // Assert
            response.EnsureSuccessStatusCode();
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var responseData = await response.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            
            var userResponse = JsonSerializer.Deserialize<UserResponse>(responseData, options);
            userResponse.Should().NotBeNull();
            sourceCount.Should().Be(userResponse.Users.Count);
        }

        [Fact]
        public async Task GetUsers_NonExistingProject_ReturnsNoContent()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();

            var qcprojectId = 321;

            // Act
            var response = await _client.GetAsync($"{ApiPrefix}/{qcprojectId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task RemoveUsers_ValidRequest_ReturnsMultiStatus()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();
            

            var request = new UsersRequest
            {
                ProjectId = 111,
                UserIds = new List<int> { 123, 456 }
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");


            var httpRequest = new HttpRequestMessage(HttpMethod.Delete, $"{ApiPrefix}")
            {
                Content = content
            };

            // Act
            var response = await _client.SendAsync(httpRequest);
            var result = await response.Content.ReadFromJsonAsync<ResponseInfo>();

            // Assert
            response.EnsureSuccessStatusCode();
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            result.Should().NotBeNull();
            //result?.Count.Should().Be(expected);
        }

        [Fact]
        public async Task RemoveUsers_InValidRequest_ReturnsError()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();
           

            var httpRequest = new HttpRequestMessage(HttpMethod.Delete, $"{ApiPrefix}")
            {
                Content = null
            };

            // Act
            var response = await _client.SendAsync(httpRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.UnsupportedMediaType);
            response.Should().NotBeNull();
        }

        [Fact]
        public async Task AddAsync_ValidRequestWithCountry_ReturnsSuccess()
        {
            // Arrange
            var client = _factory.Client;
            var request = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 10001386 },
                UserIds = new List<int> { 201, 202 }
            };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            SetAuthorizationHeader();
            client.DefaultRequestHeaders.Add("userName", "testUser");
            client.DefaultRequestHeaders.Add("Custom-CountryId", "15");
            // Act
            var response = await client.PostAsync(ApiPrefix, content);
            // Assert
            response.EnsureSuccessStatusCode();
            Assert.Equal(System.Net.HttpStatusCode.MultiStatus, response.StatusCode);
        }
        [Fact]
        public async Task AddAsync_InValidRequestWithCountry_ReturnsNotFound()
        {
            // Arrange
            var client = _factory.Client;
            var request = new ProjectUserRequest
            {
                ProjectIds = new List<int> { 1000376 },
                UserIds = new List<int> { 201, 202 }
            };
            SetAuthorizationHeader();
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            client.DefaultRequestHeaders.Add("userName", "testUser");
            client.DefaultRequestHeaders.Add("Custom-CountryId", "-1");
            // Act
            var response = await client.PostAsync(ApiPrefix, content);
            // Assert
            Assert.Equal(System.Net.HttpStatusCode.Forbidden, response.StatusCode);
        }
        [Fact]
        public async Task DeleteAsync_ValidRequestWithCountry_ReturnsTrue()
        {
            // Arrange
            var client = _factory.Client;
            var request = new UsersRequest
            {
                ProjectId = 10001386,
                UserIds = new List<int> { 201, 202 }
            };
            SetAuthorizationHeader();
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            client.DefaultRequestHeaders.Add("userName", "testUser");
            client.DefaultRequestHeaders.Add("Custom-CountryId", "15");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, ApiPrefix)
            {
                Content = content
            };
            // Act
            var response = await client.SendAsync(requestMessage);
            // Assert
            response.EnsureSuccessStatusCode();
            Assert.Equal(System.Net.HttpStatusCode.MultiStatus, response.StatusCode);
        }
        [Fact]
        public async Task DeleteAsync_InValidRequestWithCountry_ReturnsNotFound()
        {
            // Arrange
            var client = _factory.Client;
            var request = new UsersRequest
            {
                ProjectId = 222,
                UserIds = new List<int> { 201, 202 }
            };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            SetAuthorizationHeader();
            client.DefaultRequestHeaders.Add("userName", "testUser");
            client.DefaultRequestHeaders.Add("Custom-CountryId", "-1");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, ApiPrefix)
            {
                Content = content
            };
            // Act
            var response = await client.SendAsync(requestMessage);
            // Assert
            Assert.Equal(System.Net.HttpStatusCode.Forbidden, response.StatusCode);
        }

        private void SetAuthorizationHeader()
        {
            if (!string.IsNullOrEmpty(_bearerToken))
            {
                _factory.Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _bearerToken);
            }
        }

        [Fact]
        public void RabbitqMQ_Sender_Validate_Data()
        {
            //Arrange
            var specificProjectId = "333-789";

            //Act
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);
            Assert.NotNull(lastMessage);
            var lastMessageObject = JObject.Parse(lastMessage);
            var syncingEntityIds = lastMessageObject["SyncingEntityId"];

            //Assert
            if (syncingEntityIds != null)
            {
                var lastMessageId = syncingEntityIds;
                // Assert
                Assert.NotNull(lastMessageId);
                Assert.Equal(specificProjectId, lastMessageId);
            }
            else
            {
                Assert.Fail("SyncingEntityId array is null or empty.");
            }
        }

        [Fact]
        public async Task AddAsync_ValidRequest_UniqueCase_Should_CreateOutboxItem()
        {
            // Arrange
            var _client = _factory.Client;
            RemoveAllRecords();
            SetAuthorizationHeader();

            var projectId = 111;
            var userId = 123;

            var request = new ProjectUserRequest
            {
                ProjectIds = new List<int> { projectId },
                UserIds = new List<int> { userId },
                ProjectTypeId = 1
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync(ApiPrefix, content);
            response.EnsureSuccessStatusCode();

            // Assert OutBoxItem table
            var outboxItem = await _postgreSqlDbContext.OutBoxItem
                .FirstOrDefaultAsync(oi => oi.TypeId == OutboxMessageTypes.BPSecurityCreate.ToString());

            Assert.NotNull(outboxItem);
            Assert.Equal(OutboxMessageTypes.BPSecurityCreate.ToString(), outboxItem.TypeId);
        }

        [Fact]
        public async Task RemoveUsers_ValidRequest_Should_CreateOutboxItem()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();

            var projectId = 111;
            var userId = 123;

            // 🔹 Ensure the user is already in the project before calling the API
            var existingUser = await _postgreSqlDbContext.ProjectUsers
                .FirstOrDefaultAsync(pu => pu.ProjectId == projectId && pu.UserId == userId);

            if (existingUser == null)
            {
                _output.WriteLine($"❌ Test setup failed: User {userId} is not assigned to Project {projectId}");
                throw new Exception("Test setup failed: No user found.");
            }

            var request = new UsersRequest
            {
                ProjectId = projectId,
                UserIds = new List<int> { userId },
                ProjectTypeId = 1
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Explicitly send DELETE request with a JSON body
            var httpRequest = new HttpRequestMessage(HttpMethod.Delete, $"{ApiPrefix}")
            {
                Content = content
            };

            // Act
            var response = await _client.SendAsync(httpRequest);

            // 🔹 Log the response if it fails
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _output.WriteLine($"❌ Response Status Code: {response.StatusCode}");
                _output.WriteLine($"❌ Response Content: {errorContent}");
            }

            response.EnsureSuccessStatusCode(); // This will throw an exception if the response is not 2xx

            // Assert that an OutboxItem was created
            var outboxItem = await _postgreSqlDbContext.OutBoxItem
                .FirstOrDefaultAsync(oi => oi.TypeId == OutboxMessageTypes.BPSecurityDelete.ToString());

            Assert.NotNull(outboxItem);
            Assert.Equal(OutboxMessageTypes.BPSecurityDelete.ToString(), outboxItem.TypeId);
        }




        private void RemoveAllRecords()
        {
            var projectUsers = _postgreSqlDbContext.ProjectUsers.ToList();
            _postgreSqlDbContext.ProjectUsers.RemoveRange(projectUsers);
            var users = _postgreSqlDbContext.ProjectUsers.ToList();
            _postgreSqlDbContext.ProjectUsers.RemoveRange(users);
            _postgreSqlDbContext.SaveChanges();
        }

        private string GetLastMessageFromQueue(string queueName)
        {
            _channel.QueueDeclareAsync(queue: queueName,
                                  durable: true,
                                  exclusive: false,
                                  autoDelete: false,
                                  arguments: null);

            BasicGetResult result = null;
            string lastMessage = string.Empty;

            while ((result = _channel.BasicGetAsync(queueName, true).Result) != null)
            {
                var body = result.Body.ToArray();
                lastMessage = Encoding.UTF8.GetString(body);
            }
            return string.IsNullOrEmpty(lastMessage) ? string.Empty : lastMessage;
        }

        #region BulkCopyAsync Integration Tests

        [Fact]
        public async Task BulkCopyAsync_ValidRequest_ReturnsMultiStatus()
        {
            // Arrange
            var _client = _factory.Client;
            RemoveAllRecords();
            SetAuthorizationHeader();

            // Setup source projects with users
            var sourceProject1Users = new List<ProjectUserEntity>
            {
                new ProjectUserEntity { ProjectId = 111, UserId = 123, ProjectTypeId = 1, CreatedBy = "testUser", CreatedWhen = DateTimeOffset.UtcNow },
                new ProjectUserEntity { ProjectId = 111, UserId = 456, ProjectTypeId = 1, CreatedBy = "testUser", CreatedWhen = DateTimeOffset.UtcNow }
            };
            var sourceProject2Users = new List<ProjectUserEntity>
            {
                new ProjectUserEntity { ProjectId = 222, UserId = 789, ProjectTypeId = 1, CreatedBy = "testUser", CreatedWhen = DateTimeOffset.UtcNow }
            };

            _postgreSqlDbContext.ProjectUsers.AddRange(sourceProject1Users);
            _postgreSqlDbContext.ProjectUsers.AddRange(sourceProject2Users);
            await _postgreSqlDbContext.SaveChangesAsync();

            var request = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1, // BP Security
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 111, NewBaseProjectId = 333 },
                    new ProjectMapping { SourceProjectId = 222, NewBaseProjectId = 444 }
                }
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync($"{ApiPrefix}/bulkCopySecurityUsers", content);
            var result = await response.Content.ReadFromJsonAsync<ProjectUserResponse>();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            result.Should().NotBeNull();
            result.ProjectResponses.Should().HaveCount(2);
            result.ProjectResponses.Should().AllSatisfy(pr =>
            {
                pr.StatusCode.Should().Be(200);
                pr.StatusMsg.Should().Be("All users assigned successfully");
            });

            // Verify users were copied to target projects
            var targetProject1Users = await _postgreSqlDbContext.ProjectUsers
                .Where(pu => pu.ProjectId == 333 && !pu.Deleted)
                .ToListAsync();
            var targetProject2Users = await _postgreSqlDbContext.ProjectUsers
                .Where(pu => pu.ProjectId == 444 && !pu.Deleted)
                .ToListAsync();

            targetProject1Users.Should().HaveCount(3); // 2 from source project 111 + 1 from source project 222
            targetProject2Users.Should().HaveCount(3); // 2 from source project 111 + 1 from source project 222
        }

        [Fact]
        public async Task BulkCopyAsync_InvalidProjectType_ReturnsBadRequest()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();

            var request = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 2, // QC Security - not supported
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 111, NewBaseProjectId = 333 }
                }
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync($"{ApiPrefix}/bulkCopySecurityUsers", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            responseContent.Should().Contain("Bulk copy is only supported for BP Security projects");
        }

        [Fact]
        public async Task BulkCopyAsync_EmptyProjectMappings_ReturnsBadRequest()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();

            var request = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1,
                ProjectMappings = new List<ProjectMapping>() // Empty mappings
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync($"{ApiPrefix}/bulkCopySecurityUsers", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task BulkCopyAsync_DuplicateSourceProjects_ReturnsBadRequest()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();

            var request = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1,
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 111, NewBaseProjectId = 333 },
                    new ProjectMapping { SourceProjectId = 111, NewBaseProjectId = 444 } // Duplicate source
                }
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync($"{ApiPrefix}/bulkCopySecurityUsers", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task BulkCopyAsync_ExistingUsersInTarget_SkipsDuplicates()
        {
            // Arrange
            var _client = _factory.Client;
            RemoveAllRecords();
            SetAuthorizationHeader();

            // Setup source project with users
            var sourceProjectUsers = new List<ProjectUserEntity>
            {
                new ProjectUserEntity { ProjectId = 111, UserId = 123, ProjectTypeId = 1, CreatedBy = "testUser", CreatedWhen = DateTimeOffset.UtcNow },
                new ProjectUserEntity { ProjectId = 111, UserId = 456, ProjectTypeId = 1, CreatedBy = "testUser", CreatedWhen = DateTimeOffset.UtcNow }
            };

            // Setup target project with one existing user
            var targetProjectUsers = new List<ProjectUserEntity>
            {
                new ProjectUserEntity { ProjectId = 333, UserId = 123, ProjectTypeId = 1, CreatedBy = "existingUser", CreatedWhen = DateTimeOffset.UtcNow }
            };

            _postgreSqlDbContext.ProjectUsers.AddRange(sourceProjectUsers);
            _postgreSqlDbContext.ProjectUsers.AddRange(targetProjectUsers);
            await _postgreSqlDbContext.SaveChangesAsync();

            var request = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1,
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 111, NewBaseProjectId = 333 }
                }
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync($"{ApiPrefix}/bulkCopySecurityUsers", content);
            var result = await response.Content.ReadFromJsonAsync<ProjectUserResponse>();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            result.Should().NotBeNull();
            result.ProjectResponses.Should().ContainSingle();

            // Verify only new user was added (user 456), existing user 123 was skipped
            var finalTargetUsers = await _postgreSqlDbContext.ProjectUsers
                .Where(pu => pu.ProjectId == 333 && !pu.Deleted)
                .ToListAsync();

            finalTargetUsers.Should().HaveCount(2); // 1 existing + 1 new
            finalTargetUsers.Should().Contain(u => u.UserId == 123 && u.CreatedBy == "existingUser");
            finalTargetUsers.Should().Contain(u => u.UserId == 456 && u.CreatedBy == "testUser");
        }

        [Fact]
        public async Task BulkCopyAsync_NoUsersInSourceProjects_ReturnsMultiStatusWithEmptyResponse()
        {
            // Arrange
            var _client = _factory.Client;
            RemoveAllRecords();
            SetAuthorizationHeader();

            var request = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1,
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 111, NewBaseProjectId = 333 }
                }
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync($"{ApiPrefix}/bulkCopySecurityUsers", content);
            var result = await response.Content.ReadFromJsonAsync<ProjectUserResponse>();

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            result.Should().NotBeNull();
            result.ProjectResponses.Should().BeEmpty(); // No projects to process since no users found

            // Verify no users were added to target project
            var targetUsers = await _postgreSqlDbContext.ProjectUsers
                .Where(pu => pu.ProjectId == 333 && !pu.Deleted)
                .ToListAsync();

            targetUsers.Should().BeEmpty();
        }

        [Fact]
        public async Task BulkCopyAsync_MissingUserNameHeader_ReturnsBadRequest()
        {
            // Arrange
            var _client = _factory.Client;
            SetAuthorizationHeader();

            var request = new BulkCopyProjectUserRequest
            {
                ProjectTypeId = 1,
                ProjectMappings = new List<ProjectMapping>
                {
                    new ProjectMapping { SourceProjectId = 111, NewBaseProjectId = 333 }
                }
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            // Note: Not adding userName header

            // Act
            var response = await _client.PostAsync($"{ApiPrefix}/bulkCopySecurityUsers", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        #endregion
    }

    [CollectionDefinition("Non-Parallel Collection", DisableParallelization = true)]
    public class NonParallelCollectionDefinition { }

}
