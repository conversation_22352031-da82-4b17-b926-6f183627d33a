﻿using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

namespace Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ
{
    public class MessageConsumer : IMessageConsumer
    {
        private readonly ILogger<MessageConsumer> _logger;
        public IChannel _channel;
        public IConnection _connection;
        IMessageSync _msgSync;
        IErrorHandler _errorHandler;

        public MessageConsumer(IRabbitMQConnectionFactory connectionFactory, IMessageSync msgSync, ILogger<MessageConsumer> logger, IErrorHandler errorHandler)
        {
            _logger = logger;
            _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
            _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();
            _msgSync = msgSync;
            _errorHandler = errorHandler;
        }

        public async Task ConsumeAndSync()
        {
            try
            {
                var queueName = RMQConstants.ProjectServicesSecurityQueue;
                var consumer = new AsyncEventingBasicConsumer(_channel);

                consumer.ReceivedAsync += async (model, ea) =>
                {
                    try
                    {
                        await _msgSync.PerformSync(ea);
                        await _channel.BasicAckAsync(ea.DeliveryTag, false);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing message");
                        _errorHandler.HandleErrorMessages(_channel, ea);
                    }
                };

                await _channel.BasicConsumeAsync(queueName, false, consumer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing message");
            }
        }
    }
}
