﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Builder.ProjectServicesSecurity.API.Domain.Enum;

namespace Builder.ProjectServicesSecurity.API.Persistence.Entities
{
    [Table("OutBoxItem")]

    public class OutBoxItemEntity
    {
        [Required]
        public Guid Id { get; set; }
        [Required]
        public string Payload { get; set; }
        [Required]
        [Column(TypeName = "varchar(20)")]

        public OutboxStatus Status { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; }
        public DateTime? ProcessedAt { get; set; }

        [Required]
        public string TypeId { get; set; }
    }
}
