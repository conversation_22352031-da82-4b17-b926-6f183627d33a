{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "connectionStrings": {"PostgreSQL": {"DefaultConnection": "Server=localhost;Port=5432;Database=qcsecurity_db_t3;UserId=postgres;Password=", "Password": ""}, "RabbitMQ": {"HostName": "", "UserName": "", "Password": "", "VirtualHost": ""}}, "ExchangeEntitySettings": {"ProjectServicesSecurityExchangeEnabled": true}, "RetryLimit": {"Value": 3}, "Cors": {"PolicyName": "ProjectServicesSecurityPolicy", "Origins": "http://localhost:4200", "ExposedHeaders": "traceparent", "Enable": true}, "CustomException": {"TraceDetails": false}, "Health": {"Pattern": "/health"}, "Prometheus": {"Enable": "true", "UseHttpMetrics": "true", "Pattern": "/metrics"}, "Swagger": {"Enable": true, "ArtifactId": "ProjectServicesSecurity_API", "AppName": "ProjectServicesSecurity.API", "Title": "ProjectServices Security", "Version": "v1", "EnableXMLPath": true, "EnableBearerAuth": true, "DevelopmentContractPath": "contracts", "XmlFileName": "Builder.ProjectServicesSecurity.API.xml"}, "AzureAdB2C": {"Instance": "https://gfkdataplatform.b2clogin.com", "ClientId": "#{AzureAdB2C:ClientId}", "TenantId": "#{AzureAdB2C:TenantId}", "Domain": "GfkDataPlatform.onmicrosoft.com", "SignUpSignInPolicyId": "B2C_1A_SUSI_INTERNAL", "AdditionalSignUpSignInPolicyId": "B2C_1_signin_all"}, "AzureAdTestClient": {"Url": "https://login.microsoftonline.com", "ClientId": "#{AzureAdTestClient:ClientId}", "ClientSecret": "#{AzureAdTestClient:ClientSecret}", "Scope": "https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default"}, "Tracing": {"ServiceName": "ProjectServicesSecurity.API", "ServiceNamespace": "DEV", "Developer": "Builder", "ConsoleTracing": "false"}, "WebServiceClient": {"BaseAddress": {"ProjectServicesAPI": "#{WebServiceClient:BaseAddress:ProjectServicesAPI}", "ProjectServicesSecurityAPI": "#{WebServiceClient:BaseAddress:ProjectServicesSecurityAPI}", "UserRoleAssignmentAPI": "#{WebServiceClient:BaseAddress:UserRoleAssignmentAPI}"}}, "TokenSettings": {"ClientId": "#{TokenSettings:ClientId}", "GrantType": "client_credentials", "Scope": "https://GfkDataPlatform.onmicrosoft.com/dwh.shared.date/.default", "ClientSecret": "#{TokenSettings:ClientSecret}", "Endpoint": "https://GfkDataPlatform.b2clogin.com/tfp/GfkDataPlatform.onmicrosoft.com/B2C_1A_SUSI_INTERNAL/oauth2/v2.0/token"}}