﻿using System.ComponentModel.DataAnnotations;

namespace Builder.ProjectServicesSecurity.API.Persistence.Entities
{
    public class ProjectUserEntity
    {
        [Key]
        public int Id { get; set; }
        [Required]
        public int ProjectId { get; set; }
        [Required]
        public int UserId { get; set; }
        [Required]
        public string CreatedBy { get; set; }
        [Required]
        public DateTimeOffset CreatedWhen { get; set; }
        [Required]
        public string UpdatedBy { get; set; }
        [Required]
        public DateTimeOffset UpdatedWhen { get; set; }
        public string DeletedBy { get; set; }
        public DateTimeOffset? DeletedWhen { get; set; }
        [Required]
        public bool Deleted { get; set; }
        [Required]
        public int ProjectTypeId { get; set; }

    }
}
