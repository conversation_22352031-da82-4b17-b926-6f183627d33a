﻿using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using FluentAssertions;
using System.ComponentModel.DataAnnotations;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.Models
{
    public class ProjectUserTest
    {
        [Fact]
        public void ProjectUser_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var projectUser = new ProjectUserEntity();

            // Assert
            projectUser.Id.Should().Be(0);
            projectUser.ProjectId.Should().Be(0);
            projectUser.UserId.Should().Be(0);
            projectUser.CreatedBy.Should().BeNull(); 
            projectUser.CreatedWhen.Should().Be(DateTimeOffset.MinValue); 
            projectUser.UpdatedBy.Should().BeNull();
            projectUser.UpdatedWhen.Should().Be(DateTimeOffset.MinValue);
            projectUser.DeletedBy.Should().BeNull();
            projectUser.DeletedWhen.Should().BeNull();
            projectUser.Deleted.Should().BeFalse();
        }

        [Fact]
        public void ProjectUser_ShouldThrowException_WhenCreatedByPropertiesNotSet()
        {
            // Arrange
            var projectUser = new ProjectUserEntity
            {
                ProjectId = 1,
                UserId = 1,
                CreatedWhen = DateTimeOffset.UtcNow,
                UpdatedWhen = DateTimeOffset.UtcNow
            };

            // Act & Assert
            Action act = () => ValidateProjectUser(projectUser);
            act.Should().Throw<ValidationException>().WithMessage("*CreatedBy*");
        }

        [Fact]
        public void ProjectUser_ShouldThrowException_WhenUpdatedByPropertiesNotSet()
        {
            // Arrange
            var projectUser = new ProjectUserEntity
            {
                ProjectId = 1,
                UserId = 1,
                CreatedBy = "test",
                CreatedWhen = DateTimeOffset.UtcNow,
                UpdatedWhen = DateTimeOffset.UtcNow
            };

            // Act & Assert
            Action act = () => ValidateProjectUser(projectUser);
            act.Should().Throw<ValidationException>().WithMessage("*UpdatedBy*");
        }

        [Fact]
        public void ProjectUser_ShouldHandleSoftDelete()
        {
            // Arrange
            var projectUser = new ProjectUserEntity
            {
                ProjectId = 1,
                UserId = 1,
                DeletedBy = "admin",
                DeletedWhen = DateTimeOffset.UtcNow,
                Deleted = true
            };

            // Act & Assert
            projectUser.Deleted.Should().BeTrue();
            projectUser.DeletedBy.Should().Be("admin");
            projectUser.DeletedWhen.Should().BeCloseTo(DateTimeOffset.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void ProjectUser_ShouldHandleOptionalSoftDeleteFields()
        {
            // Arrange
            var projectUser = new ProjectUserEntity
            {
                ProjectId = 1,
                UserId = 1,
                Deleted = false
            };

            // Act & Assert
            projectUser.Deleted.Should().BeFalse();
            projectUser.DeletedBy.Should().BeNull();
            projectUser.DeletedWhen.Should().BeNull();
        }

        [Fact]
        public void ProjectUser_ShouldAssignAndRetrievePropertiesCorrectly()
        {
            // Arrange
            var projectUser = new ProjectUserEntity
            {
                Id = 1,
                ProjectId = 123,
                UserId = 456,
                CreatedBy = "creator",
                CreatedWhen = DateTimeOffset.UtcNow,
                UpdatedBy = "updater",
                UpdatedWhen = DateTimeOffset.UtcNow.AddHours(1),
                DeletedBy = "deleter",
                DeletedWhen = DateTimeOffset.UtcNow.AddHours(2),
                Deleted = true
            };

            // Act & Assert
            projectUser.Id.Should().Be(1);
            projectUser.ProjectId.Should().Be(123);
            projectUser.UserId.Should().Be(456);
            projectUser.CreatedBy.Should().Be("creator");
            projectUser.CreatedWhen.Should().BeCloseTo(DateTimeOffset.UtcNow, TimeSpan.FromSeconds(1));
            projectUser.UpdatedBy.Should().Be("updater");
            projectUser.UpdatedWhen.Should().BeCloseTo(DateTimeOffset.UtcNow.AddHours(1), TimeSpan.FromSeconds(1));
            projectUser.DeletedBy.Should().Be("deleter");
            projectUser.DeletedWhen.Should().BeCloseTo(DateTimeOffset.UtcNow.AddHours(2), TimeSpan.FromSeconds(1));
            projectUser.Deleted.Should().BeTrue();
        }

        private void ValidateProjectUser(ProjectUserEntity projectUser)
        {
            var context = new ValidationContext(projectUser, null, null);
            Validator.ValidateObject(projectUser, context, validateAllProperties: true);
        }

    }
}
