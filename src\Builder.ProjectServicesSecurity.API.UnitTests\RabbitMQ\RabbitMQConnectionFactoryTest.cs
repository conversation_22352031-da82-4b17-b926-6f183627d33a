﻿using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Configuration;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Microsoft.Extensions.Options;
using Moq;
using RabbitMQ.Client;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.RabbitMQ
{
    public class RabbitMQConnectionFactoryTests
    {
        [Fact]
        public void Constructor_NullSettings_ThrowsArgumentNullException()
        {
            // Arrange
            IOptions<RabbitMQSettings> rabbitMQSettings = null;

            // Act & Assert
            var ex = Assert.Throws<ArgumentNullException>(() => new RabbitMQConnectionFactory(rabbitMQSettings));
            Assert.Equal("RabbitMQ settings must not be null (Parameter 'rabbitMQSettings')", ex.Message);
        }

        [Theory]
        [InlineData("", "guest", "guest", "/")]
        [InlineData("localhost", "", "guest", "/")]
        [InlineData("localhost", "guest", "", "/")]
        [InlineData("localhost", "guest", "guest", "")]
        public void Constructor_InvalidSettings_ThrowsArgumentException(string hostName, string userName, string password, string virtualHost)
        {
            // Arrange
            var rabbitMQSettings = new RabbitMQSettings
            {
                HostName = hostName,
                UserName = userName,
                Password = password,
                VirtualHost = virtualHost
            };

            var optionsMock = new Mock<IOptions<RabbitMQSettings>>();
            optionsMock.Setup(x => x.Value).Returns(rabbitMQSettings);

            // Act & Assert
            var ex = Assert.Throws<ArgumentException>(() => new RabbitMQConnectionFactory(optionsMock.Object));

            if (string.IsNullOrEmpty(hostName))
                Assert.Equal("The value cannot be an empty string. (Parameter 'HostName')", ex.Message);
            else if (string.IsNullOrEmpty(userName))
                Assert.Equal("The value cannot be an empty string. (Parameter 'UserName')", ex.Message);
            else if (string.IsNullOrEmpty(password))
                Assert.Equal("The value cannot be an empty string. (Parameter 'Password')", ex.Message);
            else if (string.IsNullOrEmpty(virtualHost))
                Assert.Equal("The value cannot be an empty string. (Parameter 'VirtualHost')", ex.Message);
        }
    }
}
