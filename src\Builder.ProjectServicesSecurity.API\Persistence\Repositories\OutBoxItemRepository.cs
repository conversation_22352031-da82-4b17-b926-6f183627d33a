﻿using Builder.ProjectServicesSecurity.API.Domain.Enum;
using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Builder.ProjectServicesSecurity.API.Persistence.Repositories
{
    public class OutBoxItemRepository : IOutBoxItemRepository
    {
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly ILogger<OutBoxItemRepository> _logger;

        public OutBoxItemRepository(IServiceScopeFactory scopeFactory, ILogger<OutBoxItemRepository> logger)
        {
            _scopeFactory = scopeFactory;
            _logger = logger;
        }

        public async Task SaveMessagesAsync(object message, string TypeId)
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var outBoxItem = new OutBoxItemEntity
            {
                Payload = System.Text.Json.JsonSerializer.Serialize(message),
                TypeId = TypeId,
                Status = OutboxStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            context.Add(outBoxItem);
            await context.SaveChangesAsync();
        }
        public async Task<List<OutBoxItemEntity>> GetUnprocessedMessagesAsync()
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            return await context.OutBoxItem
                .Where(m => m.Status == OutboxStatus.Pending)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();
        }

        public async Task MarkMessageAsProcessedAsync(Guid messageId)
        {
            using var scope = _scopeFactory.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var message = await context.OutBoxItem.FindAsync(messageId);
            if (message != null)
            {
                message.Status = OutboxStatus.Processed;
                message.ProcessedAt = DateTime.UtcNow;
                await context.SaveChangesAsync();
            }
        }


    }
}
