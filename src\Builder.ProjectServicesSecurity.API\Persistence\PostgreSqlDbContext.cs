﻿using Microsoft.EntityFrameworkCore;
using Builder.ProjectServicesSecurity.API.Persistence.Entities;

namespace Builder.ProjectServicesSecurity.API.Persistence
{
    public class PostgreSqlDbContext : DbContext
    {
        public PostgreSqlDbContext(DbContextOptions<PostgreSqlDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ProjectUserEntity>().Property(p => p.CreatedWhen).HasDefaultValue(DateTimeOffset.UtcNow);
            modelBuilder.Entity<ProjectType>();
        }

        public DbSet<ProjectUserEntity> ProjectUsers { get; set; }
        public DbSet<ProjectType> ProjectType { get; set; }
        public DbSet<OutBoxItemEntity> OutBoxItem { get; set; }

    }
}
