﻿using Microsoft.VisualStudio.TestPlatform.ObjectModel;

namespace Builder.ProjectServicesSecurity.API.IntegrationTests.Infrastructure.WebAPIClient
{
    public class WebAPIService : IWebAPIService
    {       
        private readonly ITokenService _tokenService;
        private const string ProjectServicesAPI = "dp.dwh.bx.api";
        public WebAPIService(ITokenService tokenService)
        {
            _tokenService = tokenService;
        }

        public async Task<string> GetBearerToken()
        {
            var token = await _tokenService.GetTokenAsync(ProjectServicesAPI);
            return token;
        }
    }
}
