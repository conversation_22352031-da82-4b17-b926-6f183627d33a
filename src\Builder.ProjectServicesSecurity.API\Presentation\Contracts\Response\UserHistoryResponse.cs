﻿using System.Text.Json.Serialization;

namespace Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response
{
    public class UserHistoryResponse
    {
        public int Id { get; set; }
        public string AssignedBy { get; set; }
        public DateTimeOffset? AssignedOn { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string DeletedBy { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public DateTimeOffset? DeletedOn { get; set; }
    }
}
