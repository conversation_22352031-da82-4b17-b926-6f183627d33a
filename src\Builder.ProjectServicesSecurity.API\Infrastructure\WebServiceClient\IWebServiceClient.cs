﻿using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;

namespace Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient
{
    public interface IWebServiceClient
    {
        Task<ServiceResponse<T>> GetAsync<T>(string requestUri, object requestPayload);
        Task<ServiceResponse<T>> GetAsync<T>(string requestUri);
    }
}
