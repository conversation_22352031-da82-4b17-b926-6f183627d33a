﻿using Builder.ProjectServicesSecurity.API.Domain;
using System.ComponentModel.DataAnnotations;

namespace Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request
{
    public class ProjectUserRequest : IValidatableObject
    {
        public int ProjectTypeId { get; set; }
        public List<int> UserIds { get; set; }
        public List<int> ProjectIds { get; set; }
        public int? SourceBaseProjectId { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            if ((UserIds == null || !UserIds.Any()) && !SourceBaseProjectId.HasValue)
            {
                results.Add(new ValidationResult("Either a list of UserIds or a SourceBaseProjectId must be provided."));
            }

            if (UserIds != null && UserIds.Any() && SourceBaseProjectId.HasValue)
            {
                results.Add(new ValidationResult("Provide either a list of UserIds or a SourceBaseProjectId, but not both."));
            }

            if (ProjectIds == null || !ProjectIds.Any())
            {
                results.Add(new ValidationResult("Must have at least one project id to proceed"));
            }

            if (ProjectTypeId != 1 && ProjectTypeId != 2)
            {
                results.Add(new ValidationResult("Please provide valid projectTypeId"));
            }

            return results;
        }
    }
}
