﻿using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;

namespace Builder.ProjectServicesSecurity.API.Services
{
    public interface IProjectUserService
    {
        Task<Tuple<List<Project>, List<Project>>> AddAsync(ProjectDetails projectDetails);
        Task<List<UserHistory>> GetAsync(int projectId, int projectTypeId);
        Task<List<UserHistory>> GetAsyncList(Project project);
        Task<Tuple<List<int>, List<int>>> DeleteAsync(ProjectDetails projectDetails);
        Task DeleteUsersAsync(List<int> userIds, int projectTypeId);
        Task<List<UserHistory>> GetUsersForCopyAsync(int projectId, int projectTypeId);
        Task<List<UserHistory>> GetUsersByProjectIdsAsync(List<int> projectIds);
        Task<BulkOperationResult> BulkAddAsync(List<Project> projects, string userName, string countryIds, List<int> validProjectIds);
        Task<BulkOperationResult> BulkCopyAsync(BulkCopyProjectUserRequest request, string userName, string countryIds);
    }
}
