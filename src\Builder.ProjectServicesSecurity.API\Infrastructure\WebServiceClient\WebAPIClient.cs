﻿using System.Net;
using Builder.ProjectServicesSecurity.Services.Constants;
using Builder.ProjectServicesSecurity.Services.Interfaces;

namespace Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient
{
    public class WebAPIClient : BaseApiClient, IWebAPIClient
    {
        public WebAPIClient(IHttpClientFactory httpClientFactory,
            ITokenService tokenService,
            ILogger<WebAPIClient> logger,
            IPollyPolicyHelper pollyHelper)
            : base(httpClientFactory, tokenService, logger, pollyHelper)
        {
        }

        public async Task<ServiceResponse<T>> GetAsync<T>(string clientName, string requestUri)
        {
            var client = await GetHttpClientAsync(clientName, AppConstants.BuilderAPI);
            return await ExecuteWithPoliciesAsync<T>(() => client.GetAsync(requestUri), requestUri);
        }

        public async Task<ServiceResponse<T>> PostAsync<T>(string clientName, string requestUri, StringContent requestContent, string username = "")
        {
            var client = await GetHttpClientAsync(clientName, AppConstants.BuilderAPI);

            var requestMessage = new HttpRequestMessage(HttpMethod.Post, requestUri)
            {
                Content = requestContent
            };

            if (!string.IsNullOrEmpty(username))
            {
                client.DefaultRequestHeaders.Add("username", username);
            }

            return await ExecuteWithPoliciesAsync<T>(() => client.SendAsync(requestMessage), requestUri);

        }
    }

    public record ServiceResponse<T>(
                 T Data,
                 bool IsSuccess,
                 string ErrorMessage,
                 HttpStatusCode StatusCode,
                 string ReasonPhrase);
}
