﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoFixture" Version="4.18.0" />
		<PackageReference Include="FluentAssertions" Version="6.11.0" />
		<PackageReference Include="Dapper" Version="2.1.35" />
		<PackageReference Include="Moq" Version="4.18.2" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="7.0.9" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.9" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
		<PackageReference Include="WireMock.Net" Version="1.6.3" />
		<PackageReference Include="xunit" Version="2.4.2" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Builder.ProjectServicesSecurity.API\Builder.ProjectServicesSecurity.API.csproj" />
	</ItemGroup>

</Project>
