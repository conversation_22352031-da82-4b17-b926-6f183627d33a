﻿using Builder.ProjectServicesSecurity.Services.Interfaces;
using Newtonsoft.Json;

namespace Builder.ProjectServicesSecurity.Services
{
    public class TokenService : ITokenService
    {
        private readonly TokenSettings _tokenSettings;
        public TokenService(IConfiguration configuration)
        {
            _tokenSettings = configuration.GetSection("TokenSettings").Get<TokenSettings>();
        }

        public class TokenResponse
        {
            [JsonProperty("access_token")]
            public string AccessToken { get; set; }

            [JsonProperty("token_type")]
            public string TokenType { get; set; }

            [JsonProperty("expires_in")]
            public int ExpiresIn { get; set; }
        }

        public async Task<TokenResponse> GetTokenAsync(string applicationScope)
        {
            var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, _tokenSettings.Endpoint);

            var parameters = new Dictionary<string, string>
            {
                { "client_id", _tokenSettings.ClientId },
                { "grant_type", _tokenSettings.GrantType },
                { "scope", $"https://GfkDataPlatform.onmicrosoft.com/{ applicationScope }/.default"},
                { "client_secret", _tokenSettings.ClientSecret }
            };

            request.Content = new FormUrlEncodedContent(parameters);

            var response = await client.SendAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to obtain token. Status code: {response.StatusCode}");
            }

            var jsonResponse = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(jsonResponse);
            return tokenResponse;
        }
    }
}
