variables:
  APP_NAME: ProjectServicesSecurity-api
  APP_DLL_PATH: Builder.ProjectServicesSecurity.API.dll
  TEST_PROJECT_NAME: Builder.ProjectServicesSecurity.API.UnitTests
  DOJO_ENGAGEMENT_NAME: ProjectServicesSecurity-api
  SQ_PRODUCT_NAME: Builder.ProjectServicesSecurity.API
  SQ_PROJECT_KEY: Builder.ProjectServicesSecurity.API
  TEST_SETTINGS_FILE: cobertura.runsettings

include:
  - project: 'dp/de/shared/pipeline/generic-pipelines'
    file: 'dotnet/dotnet-8.yml'
