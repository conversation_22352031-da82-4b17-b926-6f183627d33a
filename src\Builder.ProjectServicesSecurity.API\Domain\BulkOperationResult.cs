namespace Builder.ProjectServicesSecurity.API.Domain
{

    public class BulkOperationResult
    {

        public List<Project> SuccessfulProjects { get; set; } = new List<Project>();
        public List<Project> FailedProjects { get; set; } = new List<Project>();

        public BulkOperationResult()
        {
        }

        public BulkOperationResult(List<Project> successfulProjects, List<Project> failedProjects)
        {
            SuccessfulProjects = successfulProjects ?? new List<Project>();
            FailedProjects = failedProjects ?? new List<Project>();
        }

        public Tuple<List<Project>, List<Project>> ToTuple()
        {
            return new Tuple<List<Project>, List<Project>>(SuccessfulProjects, FailedProjects);
        }
        public static BulkOperationResult FromTuple(Tuple<List<Project>, List<Project>> tuple)
        {
            return new BulkOperationResult(tuple.Item1, tuple.Item2);
        }
    }
}
