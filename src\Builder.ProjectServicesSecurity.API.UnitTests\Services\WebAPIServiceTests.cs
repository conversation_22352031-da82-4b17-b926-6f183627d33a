﻿using System.Net;
using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Services;
using Moq;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.Services
{
    public class WebAPIServiceTests
    {
        private readonly Mock<IWebAPIClient> _mockWebApiClient;
        private readonly WebAPIService _webApiService;

        public WebAPIServiceTests()
        {
            _mockWebApiClient = new Mock<IWebAPIClient>();
            _webApiService = new WebAPIService(_mockWebApiClient.Object);
        }

        [Fact]
        public async Task GetBaseProject_ShouldReturnListOfBaseProjectIds()
        {
            // Arrange
            var baseProjectRequest = new BaseProjectCountry
            {
                BaseProjectId = new [] { 1 },
                CountryId = new [] { 2 }
            };
            var expectedResponse = new List<int> { 101, 102 };
            var serviceResponse = new ServiceResponse<List<int>>(
                Data: expectedResponse,
                IsSuccess: true,
                ErrorMessage: null,
                StatusCode: HttpStatusCode.OK,
                ReasonPhrase: "OK"
            );

            _mockWebApiClient.Setup(client => client.PostAsync<List<int>>(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<StringContent>(),
                It.IsAny<string>()
                ))
            .ReturnsAsync(serviceResponse);

            // Act
            var result = await _webApiService.GetBaseProject(baseProjectRequest);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResponse, result);
        }

        [Fact]
        public async Task GetQCProject_ShouldReturnListOfQCProjectIds()
        {
            // Arrange
            var qcProjectRequest = new QCProjectCountry
            {
                QCProjectId = new [] { 1 },
                CountryId = new [] { 2 }
            };
            var expectedResponse = new List<int> { 201, 202 };
            var serviceResponse = new ServiceResponse<List<int>>(
                Data: expectedResponse,
                IsSuccess: true,
                ErrorMessage: null,
                StatusCode: HttpStatusCode.OK,
                ReasonPhrase: "OK"
            );

            _mockWebApiClient.Setup(client => client.PostAsync<List<int>>(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<StringContent>(),
                It.IsAny<string>()
                ))
            .ReturnsAsync(serviceResponse);

            // Act
            var result = await _webApiService.GetQCProject(qcProjectRequest);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResponse, result);
        }

        [Fact]
        public async Task GetMasterUsers_ShouldReturnListOfUserIds()
        {
            // Arrange
            var expectedResponse = new List<int> { 1, 2, 3 };
            var serviceResponse = new ServiceResponse<List<int>>(
                Data: expectedResponse,
                IsSuccess: true,
                ErrorMessage: null,
                StatusCode: HttpStatusCode.OK,
                ReasonPhrase: "OK"
            );

            _mockWebApiClient.Setup(client => client.GetAsync<List<int>>(
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync(serviceResponse);

            // Act
            var result = await _webApiService.GetMasterUsers();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResponse, result);
        }

        [Fact]
        public async Task AddSecurityUsers_ShouldThrowException_WhenApiCallFails()
        {
            // Arrange
            var projectUserRequest = new ProjectUserRequest();
            var username = "testuser";
            var serviceResponse = new ServiceResponse<ProjectUserResponse>(
                Data: null,
                IsSuccess: false,
                ErrorMessage: "Bad Request",
                StatusCode: HttpStatusCode.BadRequest,
                ReasonPhrase: "Bad Request"
            );

            _mockWebApiClient.Setup(client => client.PostAsync<ProjectUserResponse>(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<StringContent>(),
                It.IsAny<string>()))
            .ReturnsAsync(serviceResponse);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<HttpRequestException>(() =>
                _webApiService.AddSecurityUsers(projectUserRequest, username));

            Assert.Contains("Error calling API", exception.Message);
        }

        [Fact]
        public async Task GetUsersByProjectId_ShouldReturnNull_WhenNotFound()
        {
            // Arrange
            int projectId = 10;
            int typeId = 1;
            var serviceResponse = new ServiceResponse<UserResponse>(
                Data: null,
                IsSuccess: false,
                ErrorMessage: "Not Found",
                StatusCode: HttpStatusCode.NotFound,
                ReasonPhrase: "Not Found"
            );

            _mockWebApiClient.Setup(client => client.GetAsync<UserResponse>(
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync(serviceResponse);

            // Act
            var result = await _webApiService.GetUsersByProjectId(projectId, typeId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task SendNotification_ShouldNotThrowException()
        {
            // Arrange
            var username = "testuser";
            var message = "Test Message";
            var retailerRequestId = 123;
            var serviceResponse = new ServiceResponse<object>(
                Data: null,
                IsSuccess: true,
                ErrorMessage: null,
                StatusCode: HttpStatusCode.OK,
                ReasonPhrase: "OK"
            );

            _mockWebApiClient.Setup(client => client.PostAsync<object>(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<StringContent>(),
                It.IsAny<string>()))
            .ReturnsAsync(serviceResponse);

            // Act
            var task = _webApiService.SendNotification(username, message, retailerRequestId);

            // Assert
            await task; // No exception should be thrown
        }
    }
}
