﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Builder.ProjectServicesSecurity.API.Presentation.Controllers
{
    /// <summary>
    /// Base API controller
    /// Any API Controller should be better to be inherited from this controller
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public abstract class ApiController : ControllerBase
    {
        internal IActionResult OkOrNoContent<T>(T value) => value == null ? NoContent() : Ok(value);
        internal IActionResult OkOrNotFound<T>(IEnumerable<T> value) => value == null || !value.Any() ? NotFound() : Ok(value);
        internal IActionResult OkOrNotFound<T>(IEnumerable<T> value, string message) => value == null || !value.Any() ? NotFound(message) : Ok(value);
        internal ActionResult<T> OkOrNoContentT<T>(T value) => value == null ? NoContent() : Ok(value);

    }
}
