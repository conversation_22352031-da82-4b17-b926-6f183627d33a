﻿using Microsoft.AspNetCore.Mvc.ApplicationModels;

namespace Builder.ProjectServicesSecurity.API
{
    public class LowercaseControllerModelConvention : IControllerModelConvention
    {
        public void Apply(ControllerModel controller)
        {
            controller.ControllerName = controller.ControllerName.ToLower();
            foreach (var action in controller.Actions)
            {
                action.ActionName = action.ActionName.ToLower();
            }
        }
    }
}
