version: '3.8'  

services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_DB: qcsecurity_testdb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: BuilderX@123##
    ports:
      - "5433:5432"  
    volumes:
      - postgres-data:/var/lib/postgresql/data 

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RA<PERSON>ITMQ_DEFAULT_PASS: guest
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"  # Default RabbitMQ port
      - "15672:15672"  # RabbitMQ Management UI

volumes:
  postgres-data:
    driver: local



