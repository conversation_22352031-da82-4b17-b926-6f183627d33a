﻿using System.ComponentModel.DataAnnotations;

namespace Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request
{
    public class UsersRequest : IValidatableObject
    {
        public List<int> UserIds { get; set; }
        public int ProjectId { get; set; }
        public int ProjectTypeId { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();
            if (UserIds.Count <= 0)
            {
                results.Add(new ValidationResult("Must have atleast one user id to proceed"));
            }

            if (ProjectId <= 0)
            {
                results.Add(new ValidationResult("Must have atleast one project id to proceed"));
            }

            if (ProjectTypeId != 1 && ProjectTypeId != 2)
            {
                results.Add(new ValidationResult("Please provide valid projectTypeId"));
            }

            return results;
        }
    }
}
