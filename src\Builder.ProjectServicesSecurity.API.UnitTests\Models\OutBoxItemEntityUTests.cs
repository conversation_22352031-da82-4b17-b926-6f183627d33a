﻿using Builder.ProjectServicesSecurity.API.Persistence.Entities;
using Builder.ProjectServicesSecurity.API.Domain.Enum;
using FluentAssertions;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.Persistence.Entities
{
    public class OutBoxItemEntityUTests
    {
        [Fact]
        public void OutBoxItemEntity_Should_SetPropertiesCorrectly()
        {
            // Arrange
            var id = Guid.NewGuid();
            var payload = "{ \"message\": \"test\" }";
            var status = OutboxStatus.Pending;
            var createdAt = DateTime.UtcNow;
            
            var typeId = "BPSecurityCreate";

            // Act
            var entity = new OutBoxItemEntity
            {
                Id = id,
                Payload = payload,
                Status = status,
                CreatedAt = createdAt,
                ProcessedAt = null,
                TypeId = typeId
            };

            // Assert
            entity.Id.Should().Be(id);
            entity.Payload.Should().Be(payload);
            entity.Status.Should().Be(status);
            entity.CreatedAt.Should().BeCloseTo(createdAt, TimeSpan.FromMilliseconds(100));
            entity.ProcessedAt.Should().BeNull();
            entity.TypeId.Should().Be(typeId);
        }

        [Fact]
        public void OutBoxItemEntity_Should_Have_ValidAnnotations()
        {
            // Arrange
            var entity = new OutBoxItemEntity
            {
                Id = Guid.NewGuid(),
                Payload = "{ \"message\": \"test\" }",
                Status = OutboxStatus.Pending,
                CreatedAt = DateTime.UtcNow,
                TypeId = "BPSecurityCreate"
            };

            // Act
            var validationContext = new ValidationContext(entity, null, null);
            var validationResults = entity.Validate(validationContext);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Fact]
        public void OutBoxItemEntity_Should_AllowNullableProcessedAt()
        {
            // Arrange
            var entity = new OutBoxItemEntity
            {
                Id = Guid.NewGuid(),
                Payload = "{ \"message\": \"test\" }",
                Status = OutboxStatus.Pending,
                CreatedAt = DateTime.UtcNow,
                ProcessedAt = null, // Nullable field
                TypeId = "BPSecurityCreate"
            };

            // Act
            var validationContext = new ValidationContext(entity, null, null);
            var validationResults = entity.Validate(validationContext);

            // Assert
            validationResults.Should().BeEmpty();
            entity.ProcessedAt.Should().BeNull();
        }

    }

    public static class ValidationExtensions
    {
        public static System.Collections.Generic.IEnumerable<ValidationResult> Validate(this object obj, ValidationContext context)
        {
            var results = new System.Collections.Generic.List<ValidationResult>();
            Validator.TryValidateObject(obj, context, results, true);
            return results;
        }
    }
}
