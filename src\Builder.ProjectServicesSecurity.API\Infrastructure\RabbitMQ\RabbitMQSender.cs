﻿using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Models.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Microsoft.AspNetCore.Http.HttpResults;
using RabbitMQ.Client;
using System;
using System.Text.Json;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ
{
    public class RabbitMQSender : IRabbitMQSender, IAsyncDisposable
    {
        private readonly ILogger<RabbitMQSender> _logger;
        private readonly IConnection _connection;
        private readonly IChannel _channel;
        private static readonly string[] QueueConstants = { RMQConstants.ProjectServicesQueue };

        public RabbitMQSender(IRabbitMQConnectionFactory connectionFactory, ILogger<RabbitMQSender> logger)
        {
            _logger = logger;
            if (connectionFactory == null)
            {
                throw new ArgumentNullException(nameof(connectionFactory));
            }
            _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
            _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();
        }

        public void SendToRabbitMQ(string exchangeName, string routingKey, IProjectServicesData data)
        {
            try
            {
                ArgumentException.ThrowIfNullOrEmpty(exchangeName, nameof(exchangeName));
                ArgumentException.ThrowIfNullOrEmpty(routingKey, nameof(routingKey));
                // Validate data object
                ArgumentNullException.ThrowIfNull(data, nameof(data));

                var bodyMsg = JsonSerializer.SerializeToUtf8Bytes(data);

                _channel.BasicPublishAsync(exchangeName, routingKey, bodyMsg);

                _logger.LogDebug(
                    "Message sent to RabbitMQ. Exchange: {Exchange}, RoutingKey: {RoutingKey}, Data: {Data}",
                    exchangeName,
                    routingKey,
                    new
                    {
                        RabbitMQ = new
                        {
                            Exchange = exchangeName,
                            RoutingKey = routingKey,
                            Data = JsonSerializer.Serialize(data)
                        }
                    }
                );

                _logger.LogInformation(
                    "Message sent to RabbitMQ. Exchange: {Exchange}, RoutingKey: {RoutingKey}, Data: {Data}",
                    exchangeName,
                    routingKey,
                    new
                    {
                        RabbitMQ = new
                        {
                            Exchange = exchangeName,
                            RoutingKey = routingKey,
                            Data = JsonSerializer.Serialize(data)
                        }
                    }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending message to RabbitMQ: {ex.Message}", ex);
                throw;
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (_channel != null)
            {
                await _channel.CloseAsync();
            }

            if (_connection != null)
            {
                await _connection.CloseAsync();
            }
        }
    }
}
