﻿using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;

namespace Builder.ProjectServicesSecurity.API.Services.Interfaces
{
    public interface IWebAPIService
    {
        Task<ProjectUserResponse> AddSecurityUsers(ProjectUserRequest projectUserRequest, string username);
        Task<UserResponse> GetUsersByProjectId(int projectId, int typeId);
        public Task<List<int>> GetBaseProject(BaseProjectCountry baseProjectRequest);
        public Task<List<int>> GetQCProject(QCProjectCountry qcProjectRequest);
        public Task<List<int>> GetMasterUsers();
        public Task SendNotification(string username, string message, int retailerSeparationRequestId);
    }
}
