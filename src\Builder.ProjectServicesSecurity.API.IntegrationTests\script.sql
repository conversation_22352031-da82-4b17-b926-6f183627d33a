﻿CREATE TABLE IF NOT EXISTS public."Users"
(
    "Id" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    "Name" text COLLATE pg_catalog."default" NOT NULL,
    "FullName" text COLLATE pg_catalog."default" NOT NULL,
    "Email" text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    CONSTRAINT "PK_Users" PRIMARY KEY ("Id")
);

CREATE TABLE IF NOT EXISTS public."ProjectUsers"
(
    "Id" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1 ),
    "QCProjectId" integer NOT NULL,
    "UserId" integer NOT NULL,
    "CreatedBy" text COLLATE pg_catalog."default" NOT NULL,
    "CreatedWhen" timestamp with time zone NOT NULL DEFAULT '2024-08-29 09:53:41.104588+02'::timestamp with time zone,
    "UpdatedBy" text COLLATE pg_catalog."default" NOT NULL,
    "UpdatedWhen" timestamp with time zone NOT NULL,
    "DeletedBy" text COLLATE pg_catalog."default",
    "DeletedWhen" timestamp with time zone,
    "Deleted" boolean NOT NULL,
    CONSTRAINT "PK_ProjectUsers" PRIMARY KEY ("Id")
)
