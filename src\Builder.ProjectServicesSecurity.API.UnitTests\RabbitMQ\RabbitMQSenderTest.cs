﻿using AutoFixture;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Models;
using Builder.ProjectServicesSecurity.API.Services.Models.Interfaces;
using Microsoft.Extensions.Logging;
using Moq;
using RabbitMQ.Client;
using RabbitMQ.Client.Exceptions;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.RabbitMQ
{
    public class RabbitMQSenderTests
    {
        private readonly Mock<IRabbitMQConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<RabbitMQSender>> _mockLogger;
        private readonly Mock<IConnection> _mockConnection;
        private readonly Mock<IChannel> _mockChannel;
        private readonly RabbitMQSender _rabbitMQSender;

        public RabbitMQSenderTests()
        {
            _mockConnectionFactory = new Mock<IRabbitMQConnectionFactory>();
            _mockLogger = new Mock<ILogger<RabbitMQSender>>();
            _mockConnection = new Mock<IConnection>();
            _mockChannel = new Mock<IChannel>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnectionAsync()).ReturnsAsync(_mockConnection.Object);
            _mockConnection.Setup(conn => conn.CreateChannelAsync(null, It.IsAny<CancellationToken>())).ReturnsAsync(_mockChannel.Object);

            _rabbitMQSender = new RabbitMQSender(_mockConnectionFactory.Object, _mockLogger.Object);

        }

        [Fact]
        public void Constructor_ShouldThrowArgumentNullException_WhenConnectionFactoryIsNull()
        {
            Assert.Throws<ArgumentNullException>(() =>
                new RabbitMQSender(null, _mockLogger.Object));
        }

        [Fact]
        public void Constructor_Should_ThrowException_When_ConnectionFactory_Is_Null()
        {
            Assert.Throws<ArgumentNullException>(() => new RabbitMQSender(null, _mockLogger.Object));
        }

        [Fact]
        public void SendToRabbitMQ_Should_ThrowException_When_ExchangeName_Is_NullOrEmpty()
        {
            var data = new Mock<IProjectServicesData>().Object;

            Assert.Throws<ArgumentNullException>(() => _rabbitMQSender.SendToRabbitMQ(null, "routingKey", data));
            Assert.Throws<ArgumentException>(() => _rabbitMQSender.SendToRabbitMQ("", "routingKey", data));
        }

        [Fact]
        public void SendToRabbitMQ_Should_ThrowException_When_RoutingKey_Is_NullOrEmpty()
        {
            var data = new Mock<IProjectServicesData>().Object;

            Assert.Throws<ArgumentNullException>(() => _rabbitMQSender.SendToRabbitMQ("exchange", null, data));
            Assert.Throws<ArgumentException>(() => _rabbitMQSender.SendToRabbitMQ("exchange", "", data));
        }

        [Fact]
        public void SendToRabbitMQ_ShouldThrowArgumentNullException_WhenDataIsNull()
        {
            Assert.Throws<ArgumentNullException>(() =>
                _rabbitMQSender.SendToRabbitMQ("exchange", "routingKey", null));
        }



        [Fact]
        public void SendToRabbitMQ_ShouldLogInformation_WhenMessageIsSent()
        {
            var data = new Mock<IProjectServicesData>();
            data.Setup(d => d.SyncingEntityId).Returns("test_id");

            _rabbitMQSender.SendToRabbitMQ("exchange", "routingKey", data.Object);

            _mockLogger.Verify(logger => logger.Log(
                                LogLevel.Information,
                                It.IsAny<EventId>(),
                                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Message sent to RabbitMQ")),
                                null,
                                It.IsAny<Func<It.IsAnyType, Exception, string>>()), Times.Once);
        }

        [Fact]
        public async Task DisposeAsync_ShouldCloseConnectionAndChannel()
        {
            await _rabbitMQSender.DisposeAsync();

            _mockChannel.Verify(channel => channel.CloseAsync(It.IsAny<ushort>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockConnection.Verify(connection => connection.CloseAsync(It.IsAny<ushort>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}





