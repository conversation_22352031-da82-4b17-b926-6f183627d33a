﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WireMock.Server;

namespace DWH.ProjectServices.API.IntegrationTests.MockService
{
    public class ProjectServicesAPIServer
    {
        public WireMockServer Server { get; }

        public ProjectServicesAPIServer()
        {
            Server = WireMockServer.Start();
            ConfigureWireMockStubs(Server);
        }

        private void ConfigureWireMockStubs(WireMockServer server)
        {
            server
            .Given(WireMock.RequestBuilders.Request.Create()
            .WithPath("/api/v1/qcprojects/qcproject")
            .UsingPost()
            .WithHeader("Content-Type", "application/json")
            .WithBodyAsJson(new
            {
                QCProjectIds = new[] { 111, 222, 333 },
                CountryIds = new[] { 15 }
            }))
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBodyAsJson(new List<int> { 111 }));

            server
            .Given(WireMock.RequestBuilders.Request.Create()
            .WithPath("/api/v1/qcprojects/qcproject")
            .UsingPost()
            .WithHeader("Content-Type", "application/json")
            .WithBodyAsJson(new
            {
                QCProjectIds = new[] { 111, 222, 333 },
                CountryIds = new[] { -1 }
            }))
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(500)
                .WithBodyAsJson(new List<int> { } ));
        }


           

        public void Dispose()
        {
            Server.Stop();
            Server.Dispose();
        }
    }
}
