﻿using System.Net;
using System.Text;
using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.Services.Constants;
using Newtonsoft.Json;

namespace Builder.ProjectServicesSecurity.API.Services
{
    public class WebAPIService : IWebAPIService
    {
        private const string API_ENDPOINT_POSTPROJECTUSER = "/api/projectuser";
        private const string API_ENDPOINT_GETPROJECTUSER = "/api/projectuser/{0}/{1}";
        private const string API_ENDPOINT_NOTIFICATION = "/api/v1/notification/send";
        private const string API_ENDPOINT_BASEPROJECT = "/api/v1/baseprojects/baseproject";
        private const string API_ENDPOINT_QCPROJECT = "/api/v1/qcprojects/qcproject";
        private const string API_ENDPOINT_USERROLE = "/api/userroles/approveduserIds";
        private readonly IWebAPIClient _webApiClient;

        public WebAPIService(IWebAPIClient webApiClient)
        {
            _webApiClient = webApiClient;
        }

        public async Task<List<int>> GetBaseProject(BaseProjectCountry baseProjectRequest)
        {
            var requestPayload = new
            {
                baseProjectIds = baseProjectRequest.BaseProjectId,
                countryIds = baseProjectRequest.CountryId
            };
            var requestContent = new StringContent(JsonConvert.SerializeObject(requestPayload), Encoding.UTF8, "application/json");
            var baseProjectResponse = await _webApiClient.PostAsync<List<int>>(AppConstants.ProjectServicesAPI, API_ENDPOINT_BASEPROJECT, requestContent);
            return baseProjectResponse.Data;
        }

        public async Task<List<int>> GetQCProject(QCProjectCountry qcProjectRequest)
        {
            var requestPayload = new
            {
                qcProjectIds = qcProjectRequest.QCProjectId,
                countryIds = qcProjectRequest.CountryId
            };
            var requestContent = new StringContent(JsonConvert.SerializeObject(requestPayload), Encoding.UTF8, "application/json");
            var baseProjectResponse = await _webApiClient.PostAsync<List<int>>(AppConstants.ProjectServicesAPI, API_ENDPOINT_QCPROJECT, requestContent);
            return baseProjectResponse.Data;
        }

        public async Task<List<int>> GetMasterUsers()
        {
            var validUsersResponse = await _webApiClient.GetAsync<List<int>>(AppConstants.UserRoleAssignmentAPI, API_ENDPOINT_USERROLE);
            return validUsersResponse.Data;
        }

        public async Task<ProjectUserResponse> AddSecurityUsers(ProjectUserRequest projectUserRequest, string username)
        {
            var requestContent = new StringContent(JsonConvert.SerializeObject(projectUserRequest), Encoding.UTF8, "application/json");
            var response = await _webApiClient.PostAsync<ProjectUserResponse>(AppConstants.ProjectServicesSecurityAPI, API_ENDPOINT_POSTPROJECTUSER, requestContent, username);

            if (!response.IsSuccess)
            {
                throw new HttpRequestException($"Error calling API: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
            }
            return response.Data;
        }

        public async Task<UserResponse> GetUsersByProjectId(int projectId, int typeId)
        {
            var endpoint = string.Format(API_ENDPOINT_GETPROJECTUSER, projectId, typeId);

            var response = await _webApiClient.GetAsync<UserResponse>(AppConstants.ProjectServicesSecurityAPI, endpoint);

            if (!response.IsSuccess)
            {
                if (response.StatusCode == HttpStatusCode.NotFound)
                {

                    return null;
                }
                throw new HttpRequestException($"Error calling API to get users by project ID: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
            }

            return response.Data;
        }

        public async Task SendNotification(string username, string message, int retailerSeparationRequestId)
        {
            var requestPayload = new
            {
                message = message,
                retailerSeparationRequestId = retailerSeparationRequestId

            };
            var requestContent = new StringContent(JsonConvert.SerializeObject(requestPayload), Encoding.UTF8, "application/json");
            var response = await _webApiClient.PostAsync<object>(AppConstants.ProjectServicesAPI, API_ENDPOINT_NOTIFICATION, requestContent, username);
        }
    }
}
