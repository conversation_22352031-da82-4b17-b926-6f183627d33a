﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Builder.QCSecurity.API.Migrations
{
    /// <inheritdoc />
    public partial class AddBPSecurityDataModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "QCProjectId",
                table: "ProjectUsers",
                newName: "ProjectId");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "ProjectUsers",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 12, 5, 7, 18, 50, 973, DateTimeKind.Unspecified).AddTicks(6133), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 8, 29, 7, 53, 41, 104, DateTimeKind.Unspecified).AddTicks(5881), new TimeSpan(0, 0, 0, 0, 0)));



            migrationBuilder.CreateTable(
                name: "ProjectType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProjectType", x => x.Id);
                });

            migrationBuilder.InsertData(
           table: "ProjectType",
           columns: new[] { "Id", "Name" },
           values: new object[,]
           {
                    { "1", "BaseProject" },
                    { "2", "QCProject" }
           });


            migrationBuilder.AddColumn<int>(
         name: "ProjectTypeId",
         table: "ProjectUsers",
         type: "integer",
         nullable: false,
         defaultValue: 2);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProjectType");

            migrationBuilder.DropColumn(
                name: "ProjectTypeId",
                table: "ProjectUsers");

            migrationBuilder.RenameColumn(
                name: "ProjectId",
                table: "ProjectUsers",
                newName: "QCProjectId");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "ProjectUsers",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 8, 29, 7, 53, 41, 104, DateTimeKind.Unspecified).AddTicks(5881), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 12, 5, 7, 18, 50, 973, DateTimeKind.Unspecified).AddTicks(6133), new TimeSpan(0, 0, 0, 0, 0)));

           
        }
    }
}
