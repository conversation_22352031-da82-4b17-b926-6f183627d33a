﻿using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Moq;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Xunit;

namespace Builder.ProjectServicesSecurity.API.UnitTests.RabbitMQ
{
    public class MessageConsumerTests
    {
        Mock<ILogger<MessageConsumer>> _loggerMock;
        Mock<IRabbitMQConnectionFactory> _connectionFactoryMock;
        Mock<IMessageSync> _rabbitMQSyncMock;
        Mock<IChannel> _channelMock;
        Mock<IConnection> _connectionMock;
        Mock<IErrorHandler> _errorHandlerMock;

        public MessageConsumerTests()
        {
            _connectionFactoryMock = new Mock<IRabbitMQConnectionFactory>();
            _rabbitMQSyncMock = new Mock<IMessageSync>();
            _channelMock = new Mock<IChannel>();
            _connectionMock = new Mock<IConnection>();
            _loggerMock = new Mock<ILogger<MessageConsumer>>();
            _errorHandlerMock = new Mock<IErrorHandler>();
            _connectionFactoryMock.Setup(cf => cf.CreateConnectionAsync())
                .ReturnsAsync(_connectionMock.Object);
        }

        [Fact]
        public void Constructor_ShouldInitializeChannelAndConnection()
        {
            // Arrange & Act
            var rabbitMQConsumer = new MessageConsumer(_connectionFactoryMock.Object, _rabbitMQSyncMock.Object, _loggerMock.Object, _errorHandlerMock.Object);
            rabbitMQConsumer._channel = _channelMock.Object;

            // Assert
            Assert.NotNull(rabbitMQConsumer);
            Assert.NotNull(rabbitMQConsumer._channel);
            Assert.NotNull(rabbitMQConsumer._connection);
        }

        [Fact]
        public async Task ConsumeFromRabbitMQ_ShouldConsumeMessage()
        {
            // Arrange
            var rabbitMQConsumer = new MessageConsumer(_connectionFactoryMock.Object, _rabbitMQSyncMock.Object, _loggerMock.Object, _errorHandlerMock.Object);
            rabbitMQConsumer._channel = _channelMock.Object;

            // Act
            await rabbitMQConsumer.ConsumeAndSync();

            // Assert
            _channelMock.Verify(c => c.BasicConsumeAsync(
                It.IsAny<string>(),
                It.IsAny<bool>(),
                It.IsAny<string>(),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<IDictionary<string, object>>(),
                It.IsAny<IAsyncBasicConsumer>(),
                It.IsAny<CancellationToken>()
                ), Times.Once);
        }
    }
}
