﻿using System.Text.Json;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Persistence.Repositories;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Services.Models;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.Services.Constants;
using Microsoft.Extensions.Configuration;

namespace Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ
{
    public class OutBoxBackgroundService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<OutBoxBackgroundService> _logger;
        private readonly IRabbitMQSender _rabbitMqSender;
        private IConfiguration _configuration;
        private int _exceptionCount = 0;

        public OutBoxBackgroundService(IServiceScopeFactory serviceScopeFactory, ILogger<OutBoxBackgroundService> logger, IRabbitMQSender rabbitMqSender, IConfiguration configuration)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
            _rabbitMqSender = rabbitMqSender;
            _configuration = configuration;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            int retryMaxLimit = _configuration.GetValue("RetryLimit:Value", AppConstants.DefaultRetryLimit);
            int delayMaxLimit = _configuration.GetValue("RetryLimit:Delay", AppConstants.DefaultDelayLimit);            

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var repository = scope.ServiceProvider.GetRequiredService<IOutBoxItemRepository>();
                        var rabbitMqSender = scope.ServiceProvider.GetRequiredService<IRabbitMQSender>();

                        var messages = await repository.GetUnprocessedMessagesAsync();
                        foreach (var message in messages)
                        {
                            try
                            {
                                var (exchange, routingKey) = GetRabbitMQSettings(message.TypeId);
                                var payload = JsonSerializer.Deserialize<ProjectServicesData>(message.Payload);
                                _rabbitMqSender.SendToRabbitMQ(exchange, routingKey, payload);
                                await repository.MarkMessageAsProcessedAsync(message.Id);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"Error processing message {message.Id}: {ex.Message}");
                            }
                        }
                    }
                    await Task.CompletedTask;
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
                }
                catch (Exception ex)
                {
                    if (_exceptionCount < retryMaxLimit)
                    {
                        _logger.LogError($"Outbox Background Service error: {ex.Message}");
                        _exceptionCount++;
                    }
                    else
                    {
                        await Task.Delay(TimeSpan.FromMinutes(delayMaxLimit), stoppingToken);
                        _exceptionCount = 0;
                    }
                }
            }
        }


        private (string Exchange, string RoutingKey) GetRabbitMQSettings(string typeId)
        {
            return typeId switch
            {
                "BPSecurityCreate" => (ProjectSecurityConstants.BPSecurityExchange, RMQConstants.Create),
                "BPSecurityDelete" => (ProjectSecurityConstants.BPSecurityExchange, RMQConstants.Delete),
                "QCSecurityCreate" => (ProjectSecurityConstants.QCSecurityExchange, RMQConstants.Create),
                "QCSecurityDelete" => (ProjectSecurityConstants.QCSecurityExchange, RMQConstants.Delete),
                _ => throw new ArgumentException($"Unknown TypeId: {typeId}")
            };
        }

    }
}
