﻿using System.Linq.Expressions;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient;
using Builder.ProjectServicesSecurity.API.Persistence.Repositories;
using Builder.ProjectServicesSecurity.API.Services;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.Services;
using Builder.ProjectServicesSecurity.Services.Interfaces;

namespace Builder.ProjectServicesSecurity.API.StartupExtensions
{
    public static class ProjectServicesSecurityExtensions
    {
        public static IServiceCollection AddServices(this IServiceCollection services, IConfiguration configuration)
        {
            return services
                .AddTransient<IProjectUserService, ProjectUserService>()
                .AddTransient<ITokenService, TokenService>()
                .AddSingleton<IRabbitMQSender, RabbitMQSender>()
                .AddSingleton<IRabbitMQConnectionFactory, RabbitMQConnectionFactory>()
                .AddTransient<IMessageConsumer, MessageConsumer>()
                .AddTransient<IMessageSync, MessageSync>()
                .AddTransient<IErrorHandler, ErrorHandler>()
                .AddTransient<IPollyPolicyHelper, PollyPolicyHelper>()
                .AddTransient<IWebAPIClient, WebAPIClient>()
                .AddTransient<IWebAPIService, WebAPIService>();
        }

        public static IServiceCollection AddRepositories(this IServiceCollection services, IConfiguration configuration)
        {
            return services
                .AddTransient<IProjectUserRepository, ProjectUserRepository>()
                .AddTransient<IOutBoxItemRepository, OutBoxItemRepository>();
        }

        public static IQueryable<TSource> WhereIfExists<TSource, TResult>(this IQueryable<TSource> source, Expression<Func<TSource, bool>> predicate, IEnumerable<TResult> collections)
        {
            if (collections.Any())
                return source;
            return source.Where(predicate);
        }
    }
}
