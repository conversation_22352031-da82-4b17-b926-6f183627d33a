﻿using System.Threading;
using Builder.ProjectServicesSecurity.API.Infrastructure.RabbitMQ.Interfaces;

namespace Builder.ProjectServicesSecurity.API.Infrastructure
{
    public class ConsumerBackgroundService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<ConsumerBackgroundService> _logger;

        public ConsumerBackgroundService(IServiceScopeFactory serviceScopeFactory, ILogger<ConsumerBackgroundService> logger, IMessageConsumer messageConsumer)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("...Entered in ExecuteAsync...");
                var scope = _serviceScopeFactory.CreateScope();
                var msgConsumer = scope.ServiceProvider.GetService<IMessageConsumer>();
                await msgConsumer.ConsumeAndSync();
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Consumer Background Service error: {ex.Message}");
                await base.StopAsync(stoppingToken);
            }
        }
    }
}
