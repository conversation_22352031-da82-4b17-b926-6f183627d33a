﻿# Integration Test Setup Steps

## Step 1: Run Docker Compose
run docker-compose.yml file in command prompt

 ## Step 2: Open and connect PostGreSQL
 open PostGreSQL mangement tool e.g. pgAdmin

## Step 3: Create Database
right click on Databases, click Create -> Database. Set database name as "qcsecurity_testdb"

## Step 4: Copy appsettings.Development and paste it into API project
This is one activity which will setup the db needs for running the integration tests 

## Step 5: Access RabbitMQ Management Console
Open your web browser and go to http://localhost:15672. Log in with the default credentials:

Username: guest
Password: guest

## Step 6: Create a Virtual Host
Log in to the RabbitMQ Management Console.
Navigate to the "Admin" tab.
Under "Add a virtual host," enter a name for your virtual host (e.g., my_virtual_host) and click "Add Virtual Host."

## Step 7: Create a Queue
In the RabbitMQ Management Console, go to the "Queues" tab.
Under "Add a new queue," enter a name for your queue (e.g., my_queue) and select the virtual host created in Step 3.
Click "Add Queue."

## Step 8: Create an Exchange
Navigate to the "Exchanges" tab in the RabbitMQ Management Console.
Under "Add a new exchange," enter a name for your exchange (e.g., my_exchange), select the exchange type, and click "Add Exchange."

## Step 9: Bind Queue to Exchange
Go to the "Queues" tab.
Click on the queue you created in Step 4 (my_queue).
Scroll down to the "Bindings" section.
Enter the name of the exchange created in Step 5 (my_exchange) and click "Bind."

Now you have a RabbitMQ instance running with the Management Console, a virtual host, a queue, and an exchange. You can start sending and receiving messages through RabbitMQ.
## Step 9: Run Integration Tests
Finally, go to Test explorer and run all Integration Tests. 
