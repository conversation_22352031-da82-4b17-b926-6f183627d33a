﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoFixture" Version="4.18.0" />
		<PackageReference Include="FluentAssertions" Version="6.11.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.0" />
		<PackageReference Include="Moq" Version="4.18.2" />
		<PackageReference Include="xunit" Version="2.4.2" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="3.2.0">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Builder.ProjectServicesSecurity.API\Builder.ProjectServicesSecurity.API.csproj" />
  </ItemGroup>

</Project>
