﻿
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Builder.ProjectServicesSecurity.API.Infrastructure.WebServiceClient;
using Builder.ProjectServicesSecurity.API.IntegrationTests.Infrastructure;

namespace DWH.ProjectServices.API.IntegrationTests.MockService
{
    public class APIFactory : WebApplicationFactory<Program>, IDisposable
    {
        public ProjectServicesAPIServer ProjectServicesAPIServer { get; }
        public HttpClient Client { get; private set; }

        public APIFactory()
        {
            ProjectServicesAPIServer = new ProjectServicesAPIServer();

            Client = this.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    services.AddHttpClient<IWebAPIClient, WebAPIClient>((serviceProvider, client) =>
                    {
                        var wireMockUrl = ProjectServicesAPIServer.Server.Urls[0];
                        client.BaseAddress = new Uri(wireMockUrl);

                        // Override WebServiceClientOptions to ensure base address points to WireMock server
                        var options = serviceProvider.GetRequiredService<IOptions<WebServiceClientOptions>>().Value;
                        options.BaseAddress.ProjectServicesAPI = wireMockUrl;
                    });
                });
            }).CreateClient();
        }

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureServices(services =>
            {
                services.AddHttpClient();
                services.AddTransient<ITokenService, TokenService>();
            });
        }

        public new void Dispose()
        {
            ProjectServicesAPIServer.Dispose();
            base.Dispose();
        }
    }
}
