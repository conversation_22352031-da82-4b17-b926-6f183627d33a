[![Quality Gate Status](https://sonarqube.gfk.com/api/project_badges/measure?project=DWH.UserRoleAssignmentAPI&metric=alert_status&token=sqb_55eec34763a674ae8f1f0b0a888273a72ee5281f)](https://sonarqube.gfk.com/dashboard?id=Builder.QCSecurity.API)

# PROJECTSERVICESSECURITY-API

For more detail about the Project please visit the [Confluence page](https://adlm.nielseniq.com/confluence/display/SA/Builder+Modernization)

## Project Tech stack

| Title             | Technology            |
|-------------------|-----------------------|
| Web Framework     | Asp.net Core 8.0 / C# |
| Test Framework    | Fluent Assertions     |
| Data Base         | Postgres              |
| ORM               | EF Core               |
| API Documentation | OpenAPI/Swagger       |
| CI/CD             | GitLab                |

## Code Style

The project follows a specific code style defined in the `.editorconfig` file. To ensure consistency, developers can check it on their system by running the `dotnet format style` command.
