﻿using System.Text;
using System.Text.Json;
using Azure;
using Azure.Core;
using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Infrastructure.Common;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Services.Infrastructure.RabbitMQ.Constants;
using Builder.ProjectServicesSecurity.API.Services.Interfaces;
using Builder.ProjectServicesSecurity.API.Services.RabbitMQ.Constants;
using RabbitMQ.Client.Events;

namespace Builder.ProjectServicesSecurity.API.Services
{
    public class MessageSync : IMessageSync
    {
        private readonly ILogger<MessageSync> _logger;
        IConfiguration _configuration;
        private readonly IWebAPIService _webAPIService;

        public MessageSync(ILogger<MessageSync> logger, IConfiguration configuration
            , IWebAPIService webAPIService)
        {
            _logger = logger;
            _configuration = configuration;
            _webAPIService = webAPIService;
        }

        public async Task PerformSync(BasicDeliverEventArgs ea)
        {
            try
            {
                if (ea == null)
                {
                    _logger.LogInformation("MessageSync - Event Arguments is NULL");
                }

                var body = ea.Body.ToArray();
                var message = Encoding.UTF8.GetString(body);
                _logger.LogInformation($">>> MESSAGE JSON :: {message}");

                var projectServicesSecurityData = JsonSerializer.Deserialize<ProjectServicesSecurityData>(message);
                if (projectServicesSecurityData == null)
                {
                    _logger.LogInformation("MessageSync - Deserialization failed or message is null");
                    return;
                }

                _logger.LogInformation($"Received Id: {projectServicesSecurityData.Id} :: SyncingEntityId: {projectServicesSecurityData.SyncingEntityId}" +
                    $":: SourceProjectId: {projectServicesSecurityData.SourceProjectId} :: TargetProjectId: {projectServicesSecurityData.TargetProjectId}" +
                    $":: TypeId: {projectServicesSecurityData.TypeId} :: IndexSourceBP: {projectServicesSecurityData.IndexSourceBP} " +
                    $":: TotalSourceBP: {projectServicesSecurityData.TotalSourceBP} :: JiraId: {projectServicesSecurityData.JiraId} ");

                if (IsProjectServicesSecurityExchange(ea))
                {
                    var projects = new List<Project>();
                    var userResponse = await _webAPIService.GetUsersByProjectId(projectServicesSecurityData.SourceProjectId, projectServicesSecurityData.TypeId);

                    if (userResponse != null)
                    {
                        var userIds = userResponse.Users.Select(userId => userId.Id).ToList();
                        var projectUserRequest = new ProjectUserRequest
                        {
                            ProjectTypeId = projectServicesSecurityData.TypeId,
                            UserIds = userIds,
                            ProjectIds = new List<int> { projectServicesSecurityData.TargetProjectId }
                        };
                        await _webAPIService.AddSecurityUsers(projectUserRequest, projectServicesSecurityData.Username);
                    }

                    if (IsRequestCompleted(projectServicesSecurityData))
                    {
                        var notifyMsg = $"Request has been processed for Jira Id: {projectServicesSecurityData.JiraId} and RS Id: {projectServicesSecurityData.RetailerSeparationRequestId}";
                        await _webAPIService.SendNotification(projectServicesSecurityData.Username, notifyMsg, projectServicesSecurityData.RetailerSeparationRequestId);
                        _logger.LogInformation(notifyMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EXCEPTION - PerformSync");
            }
        }

        private bool IsRequestCompleted(ProjectServicesSecurityData pssData)
        {
            if (pssData.TypeId == (int)ProjectType.QCSecurity && (pssData.IndexSourceBP == pssData.TotalSourceBP))
            {
                return true;
            }
            return false;
        }

        private bool IsProjectServicesSecurityExchange(BasicDeliverEventArgs ea)
        {
            bool isEnabled = _configuration.GetValue("ExchangeEntitySettings:ProjectServicesSecurityExchangeEnabled", false);
            if (isEnabled)
            {
                if (ea.Exchange.Equals(ProjectSecurityConstants.ProjectSecurityExchange, StringComparison.OrdinalIgnoreCase) && ea.RoutingKey.Equals(RMQConstants.Update, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
            else
            {
                _logger.LogInformation($"ExchangeEntitySettings - ProjectServicesSecurity is disabled");
            }
            return false;
        }
    }
}
