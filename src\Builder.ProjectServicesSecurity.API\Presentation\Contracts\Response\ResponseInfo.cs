﻿using System.Text.Json.Serialization;

namespace Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response
{
    public class ResponseInfo
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public int? UserId { get; set; }
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public int? ProjectId { get; set; }

        public int StatusCode { get; set; }
        public string StatusMsg { get; set; }
    }
}
